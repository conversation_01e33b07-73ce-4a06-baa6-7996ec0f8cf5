import{f as a,r as e,j as s,g as t,X as r,w as n,U as i,x as l,S as d,y as o,af as c,ag as h,ah as m,ai as x}from"./react-vendor-BH2w6qpU.js";import{B as g}from"./card-DfBXoe59.js";import{e as p}from"./index-BSlVarex.js";const f=()=>{const f=a(),[j,b]=e.useState(!1),u=[{title:"Dashboard",path:"/admin/dashboard",icon:n},{title:"<PERSON><PERSON><PERSON> Nasabah",path:"/admin/nasabah",icon:i},{title:"<PERSON><PERSON><PERSON>",path:"/admin/kategori",icon:l},{title:"Kelola Transaksi",path:"/admin/transaksi",icon:d},{title:"Jemput Sampah",path:"/admin/penjemputan",icon:o},{title:"Pengaturan",path:"/admin/settings",icon:c}];return s.jsxs(s.Fragment,{children:[!j&&s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:()=>b(!0)}),s.jsxs("div",{className:`\n        fixed top-0 left-0 h-full bg-white shadow-xl border-r border-gray-200 z-50 transform transition-transform duration-300 ease-in-out flex flex-col\n        ${j?"-translate-x-full lg:translate-x-0 lg:w-16":"translate-x-0 w-64"}\n        lg:relative lg:transform-none\n      `,children:[s.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[!j&&s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-8 h-8 bg-gradient-green rounded-lg flex items-center justify-center",children:s.jsx(t,{className:"w-5 h-5 text-white"})}),s.jsxs("div",{children:[s.jsx("h2",{className:"font-bold text-gray-800",children:"Admin Panel"}),s.jsx("p",{className:"text-xs text-gray-500",children:"Bank Sampah Digital"})]})]}),s.jsx(g,{variant:"ghost",size:"sm",onClick:()=>b(!j),className:"lg:hidden",children:s.jsx(r,{className:"w-4 h-4"})})]}),s.jsx("nav",{className:"flex-1 p-4 space-y-2 overflow-y-auto",children:u.map((a=>s.jsxs(h,{to:a.path,className:({isActive:a})=>"flex items-center space-x-3 px-3 py-2.5 rounded-lg transition-all duration-200 group "+(a?"bg-bank-green-100 text-bank-green-700 border border-bank-green-200":"text-gray-600 hover:bg-gray-100 hover:text-gray-800"),onClick:()=>{"/admin/dashboard"!==a.path&&"/admin/nasabah"!==a.path&&"/admin/kategori"!==a.path&&"/admin/transaksi"!==a.path&&"/admin/penjemputan"!==a.path&&p.info("Fitur Dalam Pengembangan",{description:`${a.title} akan segera tersedia`})},children:[s.jsx(a.icon,{className:"w-5 h-5 flex-shrink-0"}),!j&&s.jsx("span",{className:"font-medium truncate",children:a.title})]},a.path)))}),s.jsx("div",{className:"p-4 border-t border-gray-200",children:s.jsxs(g,{variant:"ghost",onClick:()=>{localStorage.removeItem("user"),p.success("Logout Berhasil",{description:"Anda telah keluar dari sistem"}),f("/")},className:"w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50",children:[s.jsx(m,{className:"w-5 h-5 mr-3"}),!j&&s.jsx("span",{children:"Keluar"})]})})]}),s.jsx(g,{variant:"ghost",size:"sm",onClick:()=>b(!1),className:"fixed top-4 left-4 z-30 lg:hidden "+(j?"block":"hidden"),children:s.jsx(x,{className:"w-5 h-5"})})]})};export{f as A};
