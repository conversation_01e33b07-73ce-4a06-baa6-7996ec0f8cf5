import{r as e,j as a,aH as s}from"./react-vendor-C9kdeAq4.js";import{a as r}from"./utils-vendor-DeZn2tlB.js";import{c as t}from"./index-BZfOZLOv.js";const o=r("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=e.forwardRef((({className:e,variant:r,size:d,asChild:n=!1,...i},c)=>{const l=n?s:"button";return a.jsx(l,{className:t(o({variant:r,size:d,className:e})),ref:c,...i})}));d.displayName="Button";const n=e.forwardRef((({className:e,...s},r)=>a.jsx("div",{ref:r,className:t("rounded-xl border bg-card text-card-foreground shadow",e),...s})));n.displayName="Card";const i=e.forwardRef((({className:e,...s},r)=>a.jsx("div",{ref:r,className:t("flex flex-col space-y-1.5 p-6",e),...s})));i.displayName="CardHeader";const c=e.forwardRef((({className:e,...s},r)=>a.jsx("div",{ref:r,className:t("font-semibold leading-none tracking-tight",e),...s})));c.displayName="CardTitle";const l=e.forwardRef((({className:e,...s},r)=>a.jsx("div",{ref:r,className:t("text-sm text-muted-foreground",e),...s})));l.displayName="CardDescription";const f=e.forwardRef((({className:e,...s},r)=>a.jsx("div",{ref:r,className:t("p-6 pt-0",e),...s})));f.displayName="CardContent";e.forwardRef((({className:e,...s},r)=>a.jsx("div",{ref:r,className:t("flex items-center p-6 pt-0",e),...s}))).displayName="CardFooter";export{d as B,n as C,i as a,c as b,f as c,l as d};
