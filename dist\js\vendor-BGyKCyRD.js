import{o as t,r as e,v as n}from"./react-vendor-BH2w6qpU.js";var a={exports:{}},r={};
/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
!function(t){function e(t,e){var n=t.length;t.push(e);t:for(;0<n;){var a=n-1>>>1,o=t[a];if(!(0<r(o,e)))break t;t[a]=e,t[n]=o,n=a}}function n(t){return 0===t.length?null:t[0]}function a(t){if(0===t.length)return null;var e=t[0],n=t.pop();if(n!==e){t[0]=n;t:for(var a=0,o=t.length,i=o>>>1;a<i;){var s=2*(a+1)-1,l=t[s],c=s+1,u=t[c];if(0>r(l,n))c<o&&0>r(u,l)?(t[a]=u,t[c]=n,a=c):(t[a]=l,t[s]=n,a=s);else{if(!(c<o&&0>r(u,n)))break t;t[a]=u,t[c]=n,a=c}}}return e}function r(t,e){var n=t.sortIndex-e.sortIndex;return 0!==n?n:t.id-e.id}if("object"==typeof performance&&"function"==typeof performance.now){var o=performance;t.unstable_now=function(){return o.now()}}else{var i=Date,s=i.now();t.unstable_now=function(){return i.now()-s}}var l=[],c=[],u=1,d=null,f=3,h=!1,p=!1,m=!1,g="function"==typeof setTimeout?setTimeout:null,v="function"==typeof clearTimeout?clearTimeout:null,y="undefined"!=typeof setImmediate?setImmediate:null;function b(t){for(var r=n(c);null!==r;){if(null===r.callback)a(c);else{if(!(r.startTime<=t))break;a(c),r.sortIndex=r.expirationTime,e(l,r)}r=n(c)}}function w(t){if(m=!1,b(t),!p)if(null!==n(l))p=!0,M(x);else{var e=n(c);null!==e&&B(w,e.startTime-t)}}function x(e,r){p=!1,m&&(m=!1,v(S),S=-1),h=!0;var o=f;try{for(b(r),d=n(l);null!==d&&(!(d.expirationTime>r)||e&&!P());){var i=d.callback;if("function"==typeof i){d.callback=null,f=d.priorityLevel;var s=i(d.expirationTime<=r);r=t.unstable_now(),"function"==typeof s?d.callback=s:d===n(l)&&a(l),b(r)}else a(l);d=n(l)}if(null!==d)var u=!0;else{var g=n(c);null!==g&&B(w,g.startTime-r),u=!1}return u}finally{d=null,f=o,h=!1}}"undefined"!=typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var E,k=!1,T=null,S=-1,R=5,A=-1;function P(){return!(t.unstable_now()-A<R)}function C(){if(null!==T){var e=t.unstable_now();A=e;var n=!0;try{n=T(!0,e)}finally{n?E():(k=!1,T=null)}}else k=!1}if("function"==typeof y)E=function(){y(C)};else if("undefined"!=typeof MessageChannel){var L=new MessageChannel,N=L.port2;L.port1.onmessage=C,E=function(){N.postMessage(null)}}else E=function(){g(C,0)};function M(t){T=t,k||(k=!0,E())}function B(e,n){S=g((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(t){t.callback=null},t.unstable_continueExecution=function(){p||h||(p=!0,M(x))},t.unstable_forceFrameRate=function(t){0>t||125<t||(R=0<t?Math.floor(1e3/t):5)},t.unstable_getCurrentPriorityLevel=function(){return f},t.unstable_getFirstCallbackNode=function(){return n(l)},t.unstable_next=function(t){switch(f){case 1:case 2:case 3:var e=3;break;default:e=f}var n=f;f=e;try{return t()}finally{f=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(t,e){switch(t){case 1:case 2:case 3:case 4:case 5:break;default:t=3}var n=f;f=t;try{return e()}finally{f=n}},t.unstable_scheduleCallback=function(a,r,o){var i=t.unstable_now();switch("object"==typeof o&&null!==o?o="number"==typeof(o=o.delay)&&0<o?i+o:i:o=i,a){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return a={id:u++,callback:r,priorityLevel:a,startTime:o,expirationTime:s=o+s,sortIndex:-1},o>i?(a.sortIndex=o,e(c,a),null===n(l)&&a===n(c)&&(m?(v(S),S=-1):m=!0,B(w,o-i))):(a.sortIndex=s,e(l,a),p||h||(p=!0,M(x))),a},t.unstable_shouldYield=P,t.unstable_wrapCallback=function(t){var e=f;return function(){var n=f;f=e;try{return t.apply(this,arguments)}finally{f=n}}}}(r),a.exports=r;var o=a.exports,i=Array(12).fill(0),s=({visible:e,className:n})=>t.createElement("div",{className:["sonner-loading-wrapper",n].filter(Boolean).join(" "),"data-visible":e},t.createElement("div",{className:"sonner-spinner"},i.map(((e,n)=>t.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${n}`}))))),l=t.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},t.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),c=t.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},t.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),u=t.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},t.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),d=t.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},t.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),f=t.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},t.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),t.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),h=1,p=new class{constructor(){this.subscribe=t=>(this.subscribers.push(t),()=>{let e=this.subscribers.indexOf(t);this.subscribers.splice(e,1)}),this.publish=t=>{this.subscribers.forEach((e=>e(t)))},this.addToast=t=>{this.publish(t),this.toasts=[...this.toasts,t]},this.create=t=>{var e;let{message:n,...a}=t,r="number"==typeof(null==t?void 0:t.id)||(null==(e=t.id)?void 0:e.length)>0?t.id:h++,o=this.toasts.find((t=>t.id===r)),i=void 0===t.dismissible||t.dismissible;return this.dismissedToasts.has(r)&&this.dismissedToasts.delete(r),o?this.toasts=this.toasts.map((e=>e.id===r?(this.publish({...e,...t,id:r,title:n}),{...e,...t,id:r,dismissible:i,title:n}):e)):this.addToast({title:n,...a,dismissible:i,id:r}),r},this.dismiss=t=>(this.dismissedToasts.add(t),t||this.toasts.forEach((t=>{this.subscribers.forEach((e=>e({id:t.id,dismiss:!0})))})),this.subscribers.forEach((e=>e({id:t,dismiss:!0}))),t),this.message=(t,e)=>this.create({...e,message:t}),this.error=(t,e)=>this.create({...e,message:t,type:"error"}),this.success=(t,e)=>this.create({...e,type:"success",message:t}),this.info=(t,e)=>this.create({...e,type:"info",message:t}),this.warning=(t,e)=>this.create({...e,type:"warning",message:t}),this.loading=(t,e)=>this.create({...e,type:"loading",message:t}),this.promise=(e,n)=>{if(!n)return;let a;void 0!==n.loading&&(a=this.create({...n,promise:e,type:"loading",message:n.loading,description:"function"!=typeof n.description?n.description:void 0}));let r,o=e instanceof Promise?e:e(),i=void 0!==a,s=o.then((async e=>{if(r=["resolve",e],t.isValidElement(e))i=!1,this.create({id:a,type:"default",message:e});else if(m(e)&&!e.ok){i=!1;let t="function"==typeof n.error?await n.error(`HTTP error! status: ${e.status}`):n.error,r="function"==typeof n.description?await n.description(`HTTP error! status: ${e.status}`):n.description;this.create({id:a,type:"error",message:t,description:r})}else if(void 0!==n.success){i=!1;let t="function"==typeof n.success?await n.success(e):n.success,r="function"==typeof n.description?await n.description(e):n.description;this.create({id:a,type:"success",message:t,description:r})}})).catch((async t=>{if(r=["reject",t],void 0!==n.error){i=!1;let e="function"==typeof n.error?await n.error(t):n.error,r="function"==typeof n.description?await n.description(t):n.description;this.create({id:a,type:"error",message:e,description:r})}})).finally((()=>{var t;i&&(this.dismiss(a),a=void 0),null==(t=n.finally)||t.call(n)})),l=()=>new Promise(((t,e)=>s.then((()=>"reject"===r[0]?e(r[1]):t(r[1]))).catch(e)));return"string"!=typeof a&&"number"!=typeof a?{unwrap:l}:Object.assign(a,{unwrap:l})},this.custom=(t,e)=>{let n=(null==e?void 0:e.id)||h++;return this.create({jsx:t(n),id:n,...e}),n},this.getActiveToasts=()=>this.toasts.filter((t=>!this.dismissedToasts.has(t.id))),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}},m=t=>t&&"object"==typeof t&&"ok"in t&&"boolean"==typeof t.ok&&"status"in t&&"number"==typeof t.status,g=(t,e)=>{let n=(null==e?void 0:e.id)||h++;return p.addToast({title:t,...e,id:n}),n},v=Object.assign(g,{success:p.success,info:p.info,warning:p.warning,error:p.error,custom:p.custom,message:p.message,promise:p.promise,dismiss:p.dismiss,loading:p.loading},{getHistory:()=>p.toasts,getToasts:()=>p.getActiveToasts()});function y(t){return void 0!==t.label}!function(t,{insertAt:e}={}){if("undefined"==typeof document)return;let n=document.head||document.getElementsByTagName("head")[0],a=document.createElement("style");a.type="text/css","top"===e&&n.firstChild?n.insertBefore(a,n.firstChild):n.appendChild(a),a.styleSheet?a.styleSheet.cssText=t:a.appendChild(document.createTextNode(t))}(':where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted="true"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted="true"]){transform:none}}:where([data-sonner-toaster][data-x-position="right"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position="left"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\n');var b=3,w=14;function x(...t){return t.filter(Boolean).join(" ")}var E=n=>{var a,r,o,i,h,p,m,g,v,b,w;let{invert:E,toast:k,unstyled:T,interacting:S,setHeights:R,visibleToasts:A,heights:P,index:C,toasts:L,expanded:N,removeToast:M,defaultRichColors:B,closeButton:O,style:D,cancelButtonStyle:j,actionButtonStyle:I,className:W="",descriptionClassName:$="",duration:_,position:z,gap:H,loadingIcon:Y,expandByDefault:F,classNames:V,icons:U,closeButtonAriaLabel:q="Close toast",pauseWhenPageIsHidden:X}=n,[K,J]=t.useState(null),[G,Q]=t.useState(null),[Z,tt]=t.useState(!1),[et,nt]=t.useState(!1),[at,rt]=t.useState(!1),[ot,it]=t.useState(!1),[st,lt]=t.useState(!1),[ct,ut]=t.useState(0),[dt,ft]=t.useState(0),ht=t.useRef(k.duration||_||4e3),pt=t.useRef(null),mt=t.useRef(null),gt=0===C,vt=C+1<=A,yt=k.type,bt=!1!==k.dismissible,wt=k.className||"",xt=k.descriptionClassName||"",Et=t.useMemo((()=>P.findIndex((t=>t.toastId===k.id))||0),[P,k.id]),kt=t.useMemo((()=>{var t;return null!=(t=k.closeButton)?t:O}),[k.closeButton,O]),Tt=t.useMemo((()=>k.duration||_||4e3),[k.duration,_]),St=t.useRef(0),Rt=t.useRef(0),At=t.useRef(0),Pt=t.useRef(null),[Ct,Lt]=z.split("-"),Nt=t.useMemo((()=>P.reduce(((t,e,n)=>n>=Et?t:t+e.height),0)),[P,Et]),Mt=(()=>{let[e,n]=t.useState(document.hidden);return t.useEffect((()=>{let t=()=>{n(document.hidden)};return document.addEventListener("visibilitychange",t),()=>window.removeEventListener("visibilitychange",t)}),[]),e})(),Bt=k.invert||E,Ot="loading"===yt;Rt.current=t.useMemo((()=>Et*H+Nt),[Et,Nt]),t.useEffect((()=>{ht.current=Tt}),[Tt]),t.useEffect((()=>{tt(!0)}),[]),t.useEffect((()=>{let t=mt.current;if(t){let e=t.getBoundingClientRect().height;return ft(e),R((t=>[{toastId:k.id,height:e,position:k.position},...t])),()=>R((t=>t.filter((t=>t.toastId!==k.id))))}}),[R,k.id]),t.useLayoutEffect((()=>{if(!Z)return;let t=mt.current,e=t.style.height;t.style.height="auto";let n=t.getBoundingClientRect().height;t.style.height=e,ft(n),R((t=>t.find((t=>t.toastId===k.id))?t.map((t=>t.toastId===k.id?{...t,height:n}:t)):[{toastId:k.id,height:n,position:k.position},...t]))}),[Z,k.title,k.description,R,k.id]);let Dt=t.useCallback((()=>{nt(!0),ut(Rt.current),R((t=>t.filter((t=>t.toastId!==k.id)))),setTimeout((()=>{M(k)}),200)}),[k,M,R,Rt]);return t.useEffect((()=>{if(k.promise&&"loading"===yt||k.duration===1/0||"loading"===k.type)return;let t;return N||S||X&&Mt?(()=>{if(At.current<St.current){let t=(new Date).getTime()-St.current;ht.current=ht.current-t}At.current=(new Date).getTime()})():ht.current!==1/0&&(St.current=(new Date).getTime(),t=setTimeout((()=>{var t;null==(t=k.onAutoClose)||t.call(k,k),Dt()}),ht.current)),()=>clearTimeout(t)}),[N,S,k,yt,X,Mt,Dt]),t.useEffect((()=>{k.delete&&Dt()}),[Dt,k.delete]),t.createElement("li",{tabIndex:0,ref:mt,className:x(W,wt,null==V?void 0:V.toast,null==(a=null==k?void 0:k.classNames)?void 0:a.toast,null==V?void 0:V.default,null==V?void 0:V[yt],null==(r=null==k?void 0:k.classNames)?void 0:r[yt]),"data-sonner-toast":"","data-rich-colors":null!=(o=k.richColors)?o:B,"data-styled":!(k.jsx||k.unstyled||T),"data-mounted":Z,"data-promise":!!k.promise,"data-swiped":st,"data-removed":et,"data-visible":vt,"data-y-position":Ct,"data-x-position":Lt,"data-index":C,"data-front":gt,"data-swiping":at,"data-dismissible":bt,"data-type":yt,"data-invert":Bt,"data-swipe-out":ot,"data-swipe-direction":G,"data-expanded":!!(N||F&&Z),style:{"--index":C,"--toasts-before":C,"--z-index":L.length-C,"--offset":`${et?ct:Rt.current}px`,"--initial-height":F?"auto":`${dt}px`,...D,...k.style},onDragEnd:()=>{rt(!1),J(null),Pt.current=null},onPointerDown:t=>{Ot||!bt||(pt.current=new Date,ut(Rt.current),t.target.setPointerCapture(t.pointerId),"BUTTON"!==t.target.tagName&&(rt(!0),Pt.current={x:t.clientX,y:t.clientY}))},onPointerUp:()=>{var t,e,n,a;if(ot||!bt)return;Pt.current=null;let r=Number((null==(t=mt.current)?void 0:t.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),o=Number((null==(e=mt.current)?void 0:e.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),i=(new Date).getTime()-(null==(n=pt.current)?void 0:n.getTime()),s="x"===K?r:o,l=Math.abs(s)/i;if(Math.abs(s)>=20||l>.11)return ut(Rt.current),null==(a=k.onDismiss)||a.call(k,k),Q("x"===K?r>0?"right":"left":o>0?"down":"up"),Dt(),it(!0),void lt(!1);rt(!1),J(null)},onPointerMove:t=>{var e,a,r,o;if(!Pt.current||!bt||(null==(e=window.getSelection())?void 0:e.toString().length)>0)return;let i=t.clientY-Pt.current.y,s=t.clientX-Pt.current.x,l=null!=(a=n.swipeDirections)?a:function(t){let[e,n]=t.split("-"),a=[];return e&&a.push(e),n&&a.push(n),a}(z);!K&&(Math.abs(s)>1||Math.abs(i)>1)&&J(Math.abs(s)>Math.abs(i)?"x":"y");let c={x:0,y:0};"y"===K?(l.includes("top")||l.includes("bottom"))&&(l.includes("top")&&i<0||l.includes("bottom")&&i>0)&&(c.y=i):"x"===K&&(l.includes("left")||l.includes("right"))&&(l.includes("left")&&s<0||l.includes("right")&&s>0)&&(c.x=s),(Math.abs(c.x)>0||Math.abs(c.y)>0)&&lt(!0),null==(r=mt.current)||r.style.setProperty("--swipe-amount-x",`${c.x}px`),null==(o=mt.current)||o.style.setProperty("--swipe-amount-y",`${c.y}px`)}},kt&&!k.jsx?t.createElement("button",{"aria-label":q,"data-disabled":Ot,"data-close-button":!0,onClick:Ot||!bt?()=>{}:()=>{var t;Dt(),null==(t=k.onDismiss)||t.call(k,k)},className:x(null==V?void 0:V.closeButton,null==(i=null==k?void 0:k.classNames)?void 0:i.closeButton)},null!=(h=null==U?void 0:U.close)?h:f):null,k.jsx||e.isValidElement(k.title)?k.jsx?k.jsx:"function"==typeof k.title?k.title():k.title:t.createElement(t.Fragment,null,yt||k.icon||k.promise?t.createElement("div",{"data-icon":"",className:x(null==V?void 0:V.icon,null==(p=null==k?void 0:k.classNames)?void 0:p.icon)},k.promise||"loading"===k.type&&!k.icon?k.icon||(null!=U&&U.loading?t.createElement("div",{className:x(null==V?void 0:V.loader,null==(jt=null==k?void 0:k.classNames)?void 0:jt.loader,"sonner-loader"),"data-visible":"loading"===yt},U.loading):Y?t.createElement("div",{className:x(null==V?void 0:V.loader,null==(It=null==k?void 0:k.classNames)?void 0:It.loader,"sonner-loader"),"data-visible":"loading"===yt},Y):t.createElement(s,{className:x(null==V?void 0:V.loader,null==(Wt=null==k?void 0:k.classNames)?void 0:Wt.loader),visible:"loading"===yt})):null,"loading"!==k.type?k.icon||(null==U?void 0:U[yt])||(t=>{switch(t){case"success":return l;case"info":return u;case"warning":return c;case"error":return d;default:return null}})(yt):null):null,t.createElement("div",{"data-content":"",className:x(null==V?void 0:V.content,null==(m=null==k?void 0:k.classNames)?void 0:m.content)},t.createElement("div",{"data-title":"",className:x(null==V?void 0:V.title,null==(g=null==k?void 0:k.classNames)?void 0:g.title)},"function"==typeof k.title?k.title():k.title),k.description?t.createElement("div",{"data-description":"",className:x($,xt,null==V?void 0:V.description,null==(v=null==k?void 0:k.classNames)?void 0:v.description)},"function"==typeof k.description?k.description():k.description):null),e.isValidElement(k.cancel)?k.cancel:k.cancel&&y(k.cancel)?t.createElement("button",{"data-button":!0,"data-cancel":!0,style:k.cancelButtonStyle||j,onClick:t=>{var e,n;y(k.cancel)&&bt&&(null==(n=(e=k.cancel).onClick)||n.call(e,t),Dt())},className:x(null==V?void 0:V.cancelButton,null==(b=null==k?void 0:k.classNames)?void 0:b.cancelButton)},k.cancel.label):null,e.isValidElement(k.action)?k.action:k.action&&y(k.action)?t.createElement("button",{"data-button":!0,"data-action":!0,style:k.actionButtonStyle||I,onClick:t=>{var e,n;y(k.action)&&(null==(n=(e=k.action).onClick)||n.call(e,t),!t.defaultPrevented&&Dt())},className:x(null==V?void 0:V.actionButton,null==(w=null==k?void 0:k.classNames)?void 0:w.actionButton)},k.action.label):null));var jt,It,Wt};function k(){if("undefined"==typeof window||"undefined"==typeof document)return"ltr";let t=document.documentElement.getAttribute("dir");return"auto"!==t&&t?t:window.getComputedStyle(document.documentElement).direction}function T(t,e){let n={};return[t,e].forEach(((t,e)=>{let a=1===e,r=a?"--mobile-offset":"--offset",o=a?"16px":"32px";function i(t){["top","right","bottom","left"].forEach((e=>{n[`${r}-${e}`]="number"==typeof t?`${t}px`:t}))}"number"==typeof t||"string"==typeof t?i(t):"object"==typeof t?["top","right","bottom","left"].forEach((e=>{void 0===t[e]?n[`${r}-${e}`]=o:n[`${r}-${e}`]="number"==typeof t[e]?`${t[e]}px`:t[e]})):i(o)})),n}var S=e.forwardRef((function(e,a){let{invert:r,position:o="bottom-right",hotkey:i=["altKey","KeyT"],expand:s,closeButton:l,className:c,offset:u,mobileOffset:d,theme:f="light",richColors:h,duration:m,style:g,visibleToasts:v=b,toastOptions:y,dir:x=k(),gap:S=w,loadingIcon:R,icons:A,containerAriaLabel:P="Notifications",pauseWhenPageIsHidden:C}=e,[L,N]=t.useState([]),M=t.useMemo((()=>Array.from(new Set([o].concat(L.filter((t=>t.position)).map((t=>t.position)))))),[L,o]),[B,O]=t.useState([]),[D,j]=t.useState(!1),[I,W]=t.useState(!1),[$,_]=t.useState("system"!==f?f:"undefined"!=typeof window&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),z=t.useRef(null),H=i.join("+").replace(/Key/g,"").replace(/Digit/g,""),Y=t.useRef(null),F=t.useRef(!1),V=t.useCallback((t=>{N((e=>{var n;return null!=(n=e.find((e=>e.id===t.id)))&&n.delete||p.dismiss(t.id),e.filter((({id:e})=>e!==t.id))}))}),[]);return t.useEffect((()=>p.subscribe((t=>{t.dismiss?N((e=>e.map((e=>e.id===t.id?{...e,delete:!0}:e)))):setTimeout((()=>{n.flushSync((()=>{N((e=>{let n=e.findIndex((e=>e.id===t.id));return-1!==n?[...e.slice(0,n),{...e[n],...t},...e.slice(n+1)]:[t,...e]}))}))}))}))),[]),t.useEffect((()=>{if("system"!==f)return void _(f);if("system"===f&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?_("dark"):_("light")),"undefined"==typeof window)return;let t=window.matchMedia("(prefers-color-scheme: dark)");try{t.addEventListener("change",(({matches:t})=>{_(t?"dark":"light")}))}catch(e){t.addListener((({matches:t})=>{try{_(t?"dark":"light")}catch(e){}}))}}),[f]),t.useEffect((()=>{L.length<=1&&j(!1)}),[L]),t.useEffect((()=>{let t=t=>{var e,n;i.every((e=>t[e]||t.code===e))&&(j(!0),null==(e=z.current)||e.focus()),"Escape"===t.code&&(document.activeElement===z.current||null!=(n=z.current)&&n.contains(document.activeElement))&&j(!1)};return document.addEventListener("keydown",t),()=>document.removeEventListener("keydown",t)}),[i]),t.useEffect((()=>{if(z.current)return()=>{Y.current&&(Y.current.focus({preventScroll:!0}),Y.current=null,F.current=!1)}}),[z.current]),t.createElement("section",{ref:a,"aria-label":`${P} ${H}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},M.map(((n,a)=>{var o;let[i,f]=n.split("-");return L.length?t.createElement("ol",{key:n,dir:"auto"===x?k():x,tabIndex:-1,ref:z,className:c,"data-sonner-toaster":!0,"data-theme":$,"data-y-position":i,"data-lifted":D&&L.length>1&&!s,"data-x-position":f,style:{"--front-toast-height":`${(null==(o=B[0])?void 0:o.height)||0}px`,"--width":"356px","--gap":`${S}px`,...g,...T(u,d)},onBlur:t=>{F.current&&!t.currentTarget.contains(t.relatedTarget)&&(F.current=!1,Y.current&&(Y.current.focus({preventScroll:!0}),Y.current=null))},onFocus:t=>{t.target instanceof HTMLElement&&"false"===t.target.dataset.dismissible||F.current||(F.current=!0,Y.current=t.relatedTarget)},onMouseEnter:()=>j(!0),onMouseMove:()=>j(!0),onMouseLeave:()=>{I||j(!1)},onDragEnd:()=>j(!1),onPointerDown:t=>{t.target instanceof HTMLElement&&"false"===t.target.dataset.dismissible||W(!0)},onPointerUp:()=>W(!1)},L.filter((t=>!t.position&&0===a||t.position===n)).map(((a,o)=>{var i,c;return t.createElement(E,{key:a.id,icons:A,index:o,toast:a,defaultRichColors:h,duration:null!=(i=null==y?void 0:y.duration)?i:m,className:null==y?void 0:y.className,descriptionClassName:null==y?void 0:y.descriptionClassName,invert:r,visibleToasts:v,closeButton:null!=(c=null==y?void 0:y.closeButton)?c:l,interacting:I,position:n,style:null==y?void 0:y.style,unstyled:null==y?void 0:y.unstyled,classNames:null==y?void 0:y.classNames,cancelButtonStyle:null==y?void 0:y.cancelButtonStyle,actionButtonStyle:null==y?void 0:y.actionButtonStyle,removeToast:V,toasts:L.filter((t=>t.position==a.position)),heights:B.filter((t=>t.position==a.position)),setHeights:O,expandByDefault:s,gap:S,loadingIcon:R,expanded:D,pauseWhenPageIsHidden:C,swipeDirections:e.swipeDirections})}))):null})))}));const R=["top","right","bottom","left"],A=Math.min,P=Math.max,C=Math.round,L=Math.floor,N=t=>({x:t,y:t}),M={left:"right",right:"left",bottom:"top",top:"bottom"},B={start:"end",end:"start"};function O(t,e,n){return P(t,A(e,n))}function D(t,e){return"function"==typeof t?t(e):t}function j(t){return t.split("-")[0]}function I(t){return t.split("-")[1]}function W(t){return"x"===t?"y":"x"}function $(t){return"y"===t?"height":"width"}function _(t){return["top","bottom"].includes(j(t))?"y":"x"}function z(t){return W(_(t))}function H(t){return t.replace(/start|end/g,(t=>B[t]))}function Y(t){return t.replace(/left|right|bottom|top/g,(t=>M[t]))}function F(t){return"number"!=typeof t?function(t){return{top:0,right:0,bottom:0,left:0,...t}}(t):{top:t,right:t,bottom:t,left:t}}function V(t){const{x:e,y:n,width:a,height:r}=t;return{width:a,height:r,top:n,left:e,right:e+a,bottom:n+r,x:e,y:n}}function U(t,e,n){let{reference:a,floating:r}=t;const o=_(e),i=z(e),s=$(i),l=j(e),c="y"===o,u=a.x+a.width/2-r.width/2,d=a.y+a.height/2-r.height/2,f=a[s]/2-r[s]/2;let h;switch(l){case"top":h={x:u,y:a.y-r.height};break;case"bottom":h={x:u,y:a.y+a.height};break;case"right":h={x:a.x+a.width,y:d};break;case"left":h={x:a.x-r.width,y:d};break;default:h={x:a.x,y:a.y}}switch(I(e)){case"start":h[i]-=f*(n&&c?-1:1);break;case"end":h[i]+=f*(n&&c?-1:1)}return h}async function q(t,e){var n;void 0===e&&(e={});const{x:a,y:r,platform:o,rects:i,elements:s,strategy:l}=t,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:f=!1,padding:h=0}=D(e,t),p=F(h),m=s[f?"floating"===d?"reference":"floating":d],g=V(await o.getClippingRect({element:null==(n=await(null==o.isElement?void 0:o.isElement(m)))||n?m:m.contextElement||await(null==o.getDocumentElement?void 0:o.getDocumentElement(s.floating)),boundary:c,rootBoundary:u,strategy:l})),v="floating"===d?{x:a,y:r,width:i.floating.width,height:i.floating.height}:i.reference,y=await(null==o.getOffsetParent?void 0:o.getOffsetParent(s.floating)),b=await(null==o.isElement?void 0:o.isElement(y))&&await(null==o.getScale?void 0:o.getScale(y))||{x:1,y:1},w=V(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:v,offsetParent:y,strategy:l}):v);return{top:(g.top-w.top+p.top)/b.y,bottom:(w.bottom-g.bottom+p.bottom)/b.y,left:(g.left-w.left+p.left)/b.x,right:(w.right-g.right+p.right)/b.x}}function X(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function K(t){return R.some((e=>t[e]>=0))}function J(){return"undefined"!=typeof window}function G(t){return tt(t)?(t.nodeName||"").toLowerCase():"#document"}function Q(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function Z(t){var e;return null==(e=(tt(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function tt(t){return!!J()&&(t instanceof Node||t instanceof Q(t).Node)}function et(t){return!!J()&&(t instanceof Element||t instanceof Q(t).Element)}function nt(t){return!!J()&&(t instanceof HTMLElement||t instanceof Q(t).HTMLElement)}function at(t){return!(!J()||"undefined"==typeof ShadowRoot)&&(t instanceof ShadowRoot||t instanceof Q(t).ShadowRoot)}function rt(t){const{overflow:e,overflowX:n,overflowY:a,display:r}=ut(t);return/auto|scroll|overlay|hidden|clip/.test(e+a+n)&&!["inline","contents"].includes(r)}function ot(t){return["table","td","th"].includes(G(t))}function it(t){return[":popover-open",":modal"].some((e=>{try{return t.matches(e)}catch(n){return!1}}))}function st(t){const e=lt(),n=et(t)?ut(t):t;return["transform","translate","scale","rotate","perspective"].some((t=>!!n[t]&&"none"!==n[t]))||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some((t=>(n.willChange||"").includes(t)))||["paint","layout","strict","content"].some((t=>(n.contain||"").includes(t)))}function lt(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function ct(t){return["html","body","#document"].includes(G(t))}function ut(t){return Q(t).getComputedStyle(t)}function dt(t){return et(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function ft(t){if("html"===G(t))return t;const e=t.assignedSlot||t.parentNode||at(t)&&t.host||Z(t);return at(e)?e.host:e}function ht(t){const e=ft(t);return ct(e)?t.ownerDocument?t.ownerDocument.body:t.body:nt(e)&&rt(e)?e:ht(e)}function pt(t,e,n){var a;void 0===e&&(e=[]),void 0===n&&(n=!0);const r=ht(t),o=r===(null==(a=t.ownerDocument)?void 0:a.body),i=Q(r);if(o){const t=mt(i);return e.concat(i,i.visualViewport||[],rt(r)?r:[],t&&n?pt(t):[])}return e.concat(r,pt(r,[],n))}function mt(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function gt(t){const e=ut(t);let n=parseFloat(e.width)||0,a=parseFloat(e.height)||0;const r=nt(t),o=r?t.offsetWidth:n,i=r?t.offsetHeight:a,s=C(n)!==o||C(a)!==i;return s&&(n=o,a=i),{width:n,height:a,$:s}}function vt(t){return et(t)?t:t.contextElement}function yt(t){const e=vt(t);if(!nt(e))return N(1);const n=e.getBoundingClientRect(),{width:a,height:r,$:o}=gt(e);let i=(o?C(n.width):n.width)/a,s=(o?C(n.height):n.height)/r;return i&&Number.isFinite(i)||(i=1),s&&Number.isFinite(s)||(s=1),{x:i,y:s}}const bt=N(0);function wt(t){const e=Q(t);return lt()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:bt}function xt(t,e,n,a){void 0===e&&(e=!1),void 0===n&&(n=!1);const r=t.getBoundingClientRect(),o=vt(t);let i=N(1);e&&(a?et(a)&&(i=yt(a)):i=yt(t));const s=function(t,e,n){return void 0===e&&(e=!1),!(!n||e&&n!==Q(t))&&e}(o,n,a)?wt(o):N(0);let l=(r.left+s.x)/i.x,c=(r.top+s.y)/i.y,u=r.width/i.x,d=r.height/i.y;if(o){const t=Q(o),e=a&&et(a)?Q(a):a;let n=t,r=mt(n);for(;r&&a&&e!==n;){const t=yt(r),e=r.getBoundingClientRect(),a=ut(r),o=e.left+(r.clientLeft+parseFloat(a.paddingLeft))*t.x,i=e.top+(r.clientTop+parseFloat(a.paddingTop))*t.y;l*=t.x,c*=t.y,u*=t.x,d*=t.y,l+=o,c+=i,n=Q(r),r=mt(n)}}return V({width:u,height:d,x:l,y:c})}function Et(t,e){const n=dt(t).scrollLeft;return e?e.left+n:xt(Z(t)).left+n}function kt(t,e,n){void 0===n&&(n=!1);const a=t.getBoundingClientRect();return{x:a.left+e.scrollLeft-(n?0:Et(t,a)),y:a.top+e.scrollTop}}function Tt(t,e,n){let a;if("viewport"===e)a=function(t,e){const n=Q(t),a=Z(t),r=n.visualViewport;let o=a.clientWidth,i=a.clientHeight,s=0,l=0;if(r){o=r.width,i=r.height;const t=lt();(!t||t&&"fixed"===e)&&(s=r.offsetLeft,l=r.offsetTop)}return{width:o,height:i,x:s,y:l}}(t,n);else if("document"===e)a=function(t){const e=Z(t),n=dt(t),a=t.ownerDocument.body,r=P(e.scrollWidth,e.clientWidth,a.scrollWidth,a.clientWidth),o=P(e.scrollHeight,e.clientHeight,a.scrollHeight,a.clientHeight);let i=-n.scrollLeft+Et(t);const s=-n.scrollTop;return"rtl"===ut(a).direction&&(i+=P(e.clientWidth,a.clientWidth)-r),{width:r,height:o,x:i,y:s}}(Z(t));else if(et(e))a=function(t,e){const n=xt(t,!0,"fixed"===e),a=n.top+t.clientTop,r=n.left+t.clientLeft,o=nt(t)?yt(t):N(1);return{width:t.clientWidth*o.x,height:t.clientHeight*o.y,x:r*o.x,y:a*o.y}}(e,n);else{const n=wt(t);a={x:e.x-n.x,y:e.y-n.y,width:e.width,height:e.height}}return V(a)}function St(t,e){const n=ft(t);return!(n===e||!et(n)||ct(n))&&("fixed"===ut(n).position||St(n,e))}function Rt(t,e,n){const a=nt(e),r=Z(e),o="fixed"===n,i=xt(t,!0,o,e);let s={scrollLeft:0,scrollTop:0};const l=N(0);function c(){l.x=Et(r)}if(a||!a&&!o)if(("body"!==G(e)||rt(r))&&(s=dt(e)),a){const t=xt(e,!0,o,e);l.x=t.x+e.clientLeft,l.y=t.y+e.clientTop}else r&&c();o&&!a&&r&&c();const u=!r||a||o?N(0):kt(r,s);return{x:i.left+s.scrollLeft-l.x-u.x,y:i.top+s.scrollTop-l.y-u.y,width:i.width,height:i.height}}function At(t){return"static"===ut(t).position}function Pt(t,e){if(!nt(t)||"fixed"===ut(t).position)return null;if(e)return e(t);let n=t.offsetParent;return Z(t)===n&&(n=n.ownerDocument.body),n}function Ct(t,e){const n=Q(t);if(it(t))return n;if(!nt(t)){let e=ft(t);for(;e&&!ct(e);){if(et(e)&&!At(e))return e;e=ft(e)}return n}let a=Pt(t,e);for(;a&&ot(a)&&At(a);)a=Pt(a,e);return a&&ct(a)&&At(a)&&!st(a)?n:a||function(t){let e=ft(t);for(;nt(e)&&!ct(e);){if(st(e))return e;if(it(e))return null;e=ft(e)}return null}(t)||n}const Lt={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:a,strategy:r}=t;const o="fixed"===r,i=Z(a),s=!!e&&it(e.floating);if(a===i||s&&o)return n;let l={scrollLeft:0,scrollTop:0},c=N(1);const u=N(0),d=nt(a);if((d||!d&&!o)&&(("body"!==G(a)||rt(i))&&(l=dt(a)),nt(a))){const t=xt(a);c=yt(a),u.x=t.x+a.clientLeft,u.y=t.y+a.clientTop}const f=!i||d||o?N(0):kt(i,l,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+u.x+f.x,y:n.y*c.y-l.scrollTop*c.y+u.y+f.y}},getDocumentElement:Z,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:a,strategy:r}=t;const o=[..."clippingAncestors"===n?it(e)?[]:function(t,e){const n=e.get(t);if(n)return n;let a=pt(t,[],!1).filter((t=>et(t)&&"body"!==G(t))),r=null;const o="fixed"===ut(t).position;let i=o?ft(t):t;for(;et(i)&&!ct(i);){const e=ut(i),n=st(i);n||"fixed"!==e.position||(r=null),(o?!n&&!r:!n&&"static"===e.position&&r&&["absolute","fixed"].includes(r.position)||rt(i)&&!n&&St(t,i))?a=a.filter((t=>t!==i)):r=e,i=ft(i)}return e.set(t,a),a}(e,this._c):[].concat(n),a],i=o[0],s=o.reduce(((t,n)=>{const a=Tt(e,n,r);return t.top=P(a.top,t.top),t.right=A(a.right,t.right),t.bottom=A(a.bottom,t.bottom),t.left=P(a.left,t.left),t}),Tt(e,i,r));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:Ct,getElementRects:async function(t){const e=this.getOffsetParent||Ct,n=this.getDimensions,a=await n(t.floating);return{reference:Rt(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:a.width,height:a.height}}},getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){const{width:e,height:n}=gt(t);return{width:e,height:n}},getScale:yt,isElement:et,isRTL:function(t){return"rtl"===ut(t).direction}};function Nt(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function Mt(t,e,n,a){void 0===a&&(a={});const{ancestorScroll:r=!0,ancestorResize:o=!0,elementResize:i="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:l=!1}=a,c=vt(t),u=r||o?[...c?pt(c):[],...pt(e)]:[];u.forEach((t=>{r&&t.addEventListener("scroll",n,{passive:!0}),o&&t.addEventListener("resize",n)}));const d=c&&s?function(t,e){let n,a=null;const r=Z(t);function o(){var t;clearTimeout(n),null==(t=a)||t.disconnect(),a=null}return function i(s,l){void 0===s&&(s=!1),void 0===l&&(l=1),o();const c=t.getBoundingClientRect(),{left:u,top:d,width:f,height:h}=c;if(s||e(),!f||!h)return;const p={rootMargin:-L(d)+"px "+-L(r.clientWidth-(u+f))+"px "+-L(r.clientHeight-(d+h))+"px "+-L(u)+"px",threshold:P(0,A(1,l))||1};let m=!0;function g(e){const a=e[0].intersectionRatio;if(a!==l){if(!m)return i();a?i(!1,a):n=setTimeout((()=>{i(!1,1e-7)}),1e3)}1!==a||Nt(c,t.getBoundingClientRect())||i(),m=!1}try{a=new IntersectionObserver(g,{...p,root:r.ownerDocument})}catch(v){a=new IntersectionObserver(g,p)}a.observe(t)}(!0),o}(c,n):null;let f,h=-1,p=null;i&&(p=new ResizeObserver((t=>{let[a]=t;a&&a.target===c&&p&&(p.unobserve(e),cancelAnimationFrame(h),h=requestAnimationFrame((()=>{var t;null==(t=p)||t.observe(e)}))),n()})),c&&!l&&p.observe(c),p.observe(e));let m=l?xt(t):null;return l&&function e(){const a=xt(t);m&&!Nt(m,a)&&n();m=a,f=requestAnimationFrame(e)}(),n(),()=>{var t;u.forEach((t=>{r&&t.removeEventListener("scroll",n),o&&t.removeEventListener("resize",n)})),null==d||d(),null==(t=p)||t.disconnect(),p=null,l&&cancelAnimationFrame(f)}}const Bt=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,a;const{x:r,y:o,placement:i,middlewareData:s}=e,l=await async function(t,e){const{placement:n,platform:a,elements:r}=t,o=await(null==a.isRTL?void 0:a.isRTL(r.floating)),i=j(n),s=I(n),l="y"===_(n),c=["left","top"].includes(i)?-1:1,u=o&&l?-1:1,d=D(e,t);let{mainAxis:f,crossAxis:h,alignmentAxis:p}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return s&&"number"==typeof p&&(h="end"===s?-1*p:p),l?{x:h*u,y:f*c}:{x:f*c,y:h*u}}(e,t);return i===(null==(n=s.offset)?void 0:n.placement)&&null!=(a=s.arrow)&&a.alignmentOffset?{}:{x:r+l.x,y:o+l.y,data:{...l,placement:i}}}}},Ot=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:a,placement:r}=e,{mainAxis:o=!0,crossAxis:i=!1,limiter:s={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...l}=D(t,e),c={x:n,y:a},u=await q(e,l),d=_(j(r)),f=W(d);let h=c[f],p=c[d];if(o){const t="y"===f?"bottom":"right";h=O(h+u["y"===f?"top":"left"],h,h-u[t])}if(i){const t="y"===d?"bottom":"right";p=O(p+u["y"===d?"top":"left"],p,p-u[t])}const m=s.fn({...e,[f]:h,[d]:p});return{...m,data:{x:m.x-n,y:m.y-a,enabled:{[f]:o,[d]:i}}}}}},Dt=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,a;const{placement:r,middlewareData:o,rects:i,initialPlacement:s,platform:l,elements:c}=e,{mainAxis:u=!0,crossAxis:d=!0,fallbackPlacements:f,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:p="none",flipAlignment:m=!0,...g}=D(t,e);if(null!=(n=o.arrow)&&n.alignmentOffset)return{};const v=j(r),y=_(s),b=j(s)===s,w=await(null==l.isRTL?void 0:l.isRTL(c.floating)),x=f||(b||!m?[Y(s)]:function(t){const e=Y(t);return[H(t),e,H(e)]}(s)),E="none"!==p;!f&&E&&x.push(...function(t,e,n,a){const r=I(t);let o=function(t,e,n){const a=["left","right"],r=["right","left"],o=["top","bottom"],i=["bottom","top"];switch(t){case"top":case"bottom":return n?e?r:a:e?a:r;case"left":case"right":return e?o:i;default:return[]}}(j(t),"start"===n,a);return r&&(o=o.map((t=>t+"-"+r)),e&&(o=o.concat(o.map(H)))),o}(s,m,p,w));const k=[s,...x],T=await q(e,g),S=[];let R=(null==(a=o.flip)?void 0:a.overflows)||[];if(u&&S.push(T[v]),d){const t=function(t,e,n){void 0===n&&(n=!1);const a=I(t),r=z(t),o=$(r);let i="x"===r?a===(n?"end":"start")?"right":"left":"start"===a?"bottom":"top";return e.reference[o]>e.floating[o]&&(i=Y(i)),[i,Y(i)]}(r,i,w);S.push(T[t[0]],T[t[1]])}if(R=[...R,{placement:r,overflows:S}],!S.every((t=>t<=0))){var A,P;const t=((null==(A=o.flip)?void 0:A.index)||0)+1,e=k[t];if(e){if(!("alignment"===d&&y!==_(e))||R.every((t=>t.overflows[0]>0&&_(t.placement)===y)))return{data:{index:t,overflows:R},reset:{placement:e}}}let n=null==(P=R.filter((t=>t.overflows[0]<=0)).sort(((t,e)=>t.overflows[1]-e.overflows[1]))[0])?void 0:P.placement;if(!n)switch(h){case"bestFit":{var C;const t=null==(C=R.filter((t=>{if(E){const e=_(t.placement);return e===y||"y"===e}return!0})).map((t=>[t.placement,t.overflows.filter((t=>t>0)).reduce(((t,e)=>t+e),0)])).sort(((t,e)=>t[1]-e[1]))[0])?void 0:C[0];t&&(n=t);break}case"initialPlacement":n=s}if(r!==n)return{reset:{placement:n}}}return{}}}},jt=function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){var n,a;const{placement:r,rects:o,platform:i,elements:s}=e,{apply:l=()=>{},...c}=D(t,e),u=await q(e,c),d=j(r),f=I(r),h="y"===_(r),{width:p,height:m}=o.floating;let g,v;"top"===d||"bottom"===d?(g=d,v=f===(await(null==i.isRTL?void 0:i.isRTL(s.floating))?"start":"end")?"left":"right"):(v=d,g="end"===f?"top":"bottom");const y=m-u.top-u.bottom,b=p-u.left-u.right,w=A(m-u[g],y),x=A(p-u[v],b),E=!e.middlewareData.shift;let k=w,T=x;if(null!=(n=e.middlewareData.shift)&&n.enabled.x&&(T=b),null!=(a=e.middlewareData.shift)&&a.enabled.y&&(k=y),E&&!f){const t=P(u.left,0),e=P(u.right,0),n=P(u.top,0),a=P(u.bottom,0);h?T=p-2*(0!==t||0!==e?t+e:P(u.left,u.right)):k=m-2*(0!==n||0!==a?n+a:P(u.top,u.bottom))}await l({...e,availableWidth:T,availableHeight:k});const S=await i.getDimensions(s.floating);return p!==S.width||m!==S.height?{reset:{rects:!0}}:{}}}},It=function(t){return void 0===t&&(t={}),{name:"hide",options:t,async fn(e){const{rects:n}=e,{strategy:a="referenceHidden",...r}=D(t,e);switch(a){case"referenceHidden":{const t=X(await q(e,{...r,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:K(t)}}}case"escaped":{const t=X(await q(e,{...r,altBoundary:!0}),n.floating);return{data:{escapedOffsets:t,escaped:K(t)}}}default:return{}}}}},Wt=t=>({name:"arrow",options:t,async fn(e){const{x:n,y:a,placement:r,rects:o,platform:i,elements:s,middlewareData:l}=e,{element:c,padding:u=0}=D(t,e)||{};if(null==c)return{};const d=F(u),f={x:n,y:a},h=z(r),p=$(h),m=await i.getDimensions(c),g="y"===h,v=g?"top":"left",y=g?"bottom":"right",b=g?"clientHeight":"clientWidth",w=o.reference[p]+o.reference[h]-f[h]-o.floating[p],x=f[h]-o.reference[h],E=await(null==i.getOffsetParent?void 0:i.getOffsetParent(c));let k=E?E[b]:0;k&&await(null==i.isElement?void 0:i.isElement(E))||(k=s.floating[b]||o.floating[p]);const T=w/2-x/2,S=k/2-m[p]/2-1,R=A(d[v],S),P=A(d[y],S),C=R,L=k-m[p]-P,N=k/2-m[p]/2+T,M=O(C,N,L),B=!l.arrow&&null!=I(r)&&N!==M&&o.reference[p]/2-(N<C?R:P)-m[p]/2<0,j=B?N<C?N-C:N-L:0;return{[h]:f[h]+j,data:{[h]:M,centerOffset:N-M-j,...B&&{alignmentOffset:j}},reset:B}}}),$t=function(t){return void 0===t&&(t={}),{options:t,fn(e){const{x:n,y:a,placement:r,rects:o,middlewareData:i}=e,{offset:s=0,mainAxis:l=!0,crossAxis:c=!0}=D(t,e),u={x:n,y:a},d=_(r),f=W(d);let h=u[f],p=u[d];const m=D(s,e),g="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(l){const t="y"===f?"height":"width",e=o.reference[f]-o.floating[t]+g.mainAxis,n=o.reference[f]+o.reference[t]-g.mainAxis;h<e?h=e:h>n&&(h=n)}if(c){var v,y;const t="y"===f?"width":"height",e=["top","left"].includes(j(r)),n=o.reference[d]-o.floating[t]+(e&&(null==(v=i.offset)?void 0:v[d])||0)+(e?0:g.crossAxis),a=o.reference[d]+o.reference[t]+(e?0:(null==(y=i.offset)?void 0:y[d])||0)-(e?g.crossAxis:0);p<n?p=n:p>a&&(p=a)}return{[f]:h,[d]:p}}}},_t=(t,e,n)=>{const a=new Map,r={platform:Lt,...n},o={...r.platform,_c:a};return(async(t,e,n)=>{const{placement:a="bottom",strategy:r="absolute",middleware:o=[],platform:i}=n,s=o.filter(Boolean),l=await(null==i.isRTL?void 0:i.isRTL(e));let c=await i.getElementRects({reference:t,floating:e,strategy:r}),{x:u,y:d}=U(c,a,l),f=a,h={},p=0;for(let m=0;m<s.length;m++){const{name:n,fn:o}=s[m],{x:g,y:v,data:y,reset:b}=await o({x:u,y:d,initialPlacement:a,placement:f,strategy:r,middlewareData:h,rects:c,platform:i,elements:{reference:t,floating:e}});u=null!=g?g:u,d=null!=v?v:d,h={...h,[n]:{...h[n],...y}},b&&p<=50&&(p++,"object"==typeof b&&(b.placement&&(f=b.placement),b.rects&&(c=!0===b.rects?await i.getElementRects({reference:t,floating:e,strategy:r}):b.rects),({x:u,y:d}=U(c,f,l))),m=-1)}return{x:u,y:d,placement:f,strategy:r,middlewareData:h}})(t,e,{...r,platform:o})};
/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function zt(){return zt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(t[a]=n[a])}return t},zt.apply(this,arguments)}var Ht,Yt;(Yt=Ht||(Ht={})).Pop="POP",Yt.Push="PUSH",Yt.Replace="REPLACE";const Ft="popstate";function Vt(t){return void 0===t&&(t={}),function(t,e,n,a){void 0===a&&(a={});let{window:r=document.defaultView,v5Compat:o=!1}=a,i=r.history,s=Ht.Pop,l=null,c=u();null==c&&(c=0,i.replaceState(zt({},i.state,{idx:c}),""));function u(){return(i.state||{idx:null}).idx}function d(){s=Ht.Pop;let t=u(),e=null==t?null:t-c;c=t,l&&l({action:s,location:m.location,delta:e})}function f(t,e){s=Ht.Push;let n=Kt(m.location,t,e);c=u()+1;let a=Xt(n,c),d=m.createHref(n);try{i.pushState(a,"",d)}catch(f){if(f instanceof DOMException&&"DataCloneError"===f.name)throw f;r.location.assign(d)}o&&l&&l({action:s,location:m.location,delta:1})}function h(t,e){s=Ht.Replace;let n=Kt(m.location,t,e);c=u();let a=Xt(n,c),r=m.createHref(n);i.replaceState(a,"",r),o&&l&&l({action:s,location:m.location,delta:0})}function p(t){let e="null"!==r.location.origin?r.location.origin:r.location.href,n="string"==typeof t?t:Jt(t);return n=n.replace(/ $/,"%20"),Ut(e,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,e)}let m={get action(){return s},get location(){return t(r,i)},listen(t){if(l)throw new Error("A history only accepts one active listener");return r.addEventListener(Ft,d),l=t,()=>{r.removeEventListener(Ft,d),l=null}},createHref:t=>e(r,t),createURL:p,encodeLocation(t){let e=p(t);return{pathname:e.pathname,search:e.search,hash:e.hash}},push:f,replace:h,go:t=>i.go(t)};return m}((function(t,e){let{pathname:n,search:a,hash:r}=t.location;return Kt("",{pathname:n,search:a,hash:r},e.state&&e.state.usr||null,e.state&&e.state.key||"default")}),(function(t,e){return"string"==typeof e?e:Jt(e)}),0,t)}function Ut(t,e){if(!1===t||null==t)throw new Error(e)}function qt(t,e){if(!t)try{throw new Error(e)}catch(n){}}function Xt(t,e){return{usr:t.state,key:t.key,idx:e}}function Kt(t,e,n,a){return void 0===n&&(n=null),zt({pathname:"string"==typeof t?t:t.pathname,search:"",hash:""},"string"==typeof e?Gt(e):e,{state:n,key:e&&e.key||a||Math.random().toString(36).substr(2,8)})}function Jt(t){let{pathname:e="/",search:n="",hash:a=""}=t;return n&&"?"!==n&&(e+="?"===n.charAt(0)?n:"?"+n),a&&"#"!==a&&(e+="#"===a.charAt(0)?a:"#"+a),e}function Gt(t){let e={};if(t){let n=t.indexOf("#");n>=0&&(e.hash=t.substr(n),t=t.substr(0,n));let a=t.indexOf("?");a>=0&&(e.search=t.substr(a),t=t.substr(0,a)),t&&(e.pathname=t)}return e}var Qt,Zt;function te(t,e,n){return void 0===n&&(n="/"),function(t,e,n){let a="string"==typeof e?Gt(e):e,r=pe(a.pathname||"/",n);if(null==r)return null;let o=ee(t);!function(t){t.sort(((t,e)=>t.score!==e.score?e.score-t.score:function(t,e){let n=t.length===e.length&&t.slice(0,-1).every(((t,n)=>t===e[n]));return n?t[t.length-1]-e[e.length-1]:0}(t.routesMeta.map((t=>t.childrenIndex)),e.routesMeta.map((t=>t.childrenIndex)))))}(o);let i=null;for(let s=0;null==i&&s<o.length;++s){let t=he(r);i=de(o[s],t)}return i}(t,e,n)}function ee(t,e,n,a){void 0===e&&(e=[]),void 0===n&&(n=[]),void 0===a&&(a="");let r=(t,r,o)=>{let i={relativePath:void 0===o?t.path||"":o,caseSensitive:!0===t.caseSensitive,childrenIndex:r,route:t};i.relativePath.startsWith("/")&&(Ut(i.relativePath.startsWith(a),'Absolute route path "'+i.relativePath+'" nested under path "'+a+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),i.relativePath=i.relativePath.slice(a.length));let s=ye([a,i.relativePath]),l=n.concat(i);t.children&&t.children.length>0&&(Ut(!0!==t.index,'Index routes must not have child routes. Please remove all child routes from route path "'+s+'".'),ee(t.children,e,l,s)),(null!=t.path||t.index)&&e.push({path:s,score:ue(s,t.index),routesMeta:l})};return t.forEach(((t,e)=>{var n;if(""!==t.path&&null!=(n=t.path)&&n.includes("?"))for(let a of ne(t.path))r(t,e,a);else r(t,e)})),e}function ne(t){let e=t.split("/");if(0===e.length)return[];let[n,...a]=e,r=n.endsWith("?"),o=n.replace(/\?$/,"");if(0===a.length)return r?[o,""]:[o];let i=ne(a.join("/")),s=[];return s.push(...i.map((t=>""===t?o:[o,t].join("/")))),r&&s.push(...i),s.map((e=>t.startsWith("/")&&""===e?"/":e))}(Zt=Qt||(Qt={})).data="data",Zt.deferred="deferred",Zt.redirect="redirect",Zt.error="error";const ae=/^:[\w-]+$/,re=3,oe=2,ie=1,se=10,le=-2,ce=t=>"*"===t;function ue(t,e){let n=t.split("/"),a=n.length;return n.some(ce)&&(a+=le),e&&(a+=oe),n.filter((t=>!ce(t))).reduce(((t,e)=>t+(ae.test(e)?re:""===e?ie:se)),a)}function de(t,e,n){let{routesMeta:a}=t,r={},o="/",i=[];for(let s=0;s<a.length;++s){let t=a[s],n=s===a.length-1,l="/"===o?e:e.slice(o.length)||"/",c=fe({path:t.relativePath,caseSensitive:t.caseSensitive,end:n},l),u=t.route;if(!c)return null;Object.assign(r,c.params),i.push({params:r,pathname:ye([o,c.pathname]),pathnameBase:be(ye([o,c.pathnameBase])),route:u}),"/"!==c.pathnameBase&&(o=ye([o,c.pathnameBase]))}return i}function fe(t,e){"string"==typeof t&&(t={path:t,caseSensitive:!1,end:!0});let[n,a]=function(t,e,n){void 0===e&&(e=!1);void 0===n&&(n=!0);qt("*"===t||!t.endsWith("*")||t.endsWith("/*"),'Route path "'+t+'" will be treated as if it were "'+t.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+t.replace(/\*$/,"/*")+'".');let a=[],r="^"+t.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((t,e,n)=>(a.push({paramName:e,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));t.endsWith("*")?(a.push({paramName:"*"}),r+="*"===t||"/*"===t?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?r+="\\/*$":""!==t&&"/"!==t&&(r+="(?:(?=\\/|$))");let o=new RegExp(r,e?void 0:"i");return[o,a]}(t.path,t.caseSensitive,t.end),r=e.match(n);if(!r)return null;let o=r[0],i=o.replace(/(.)\/+$/,"$1"),s=r.slice(1);return{params:a.reduce(((t,e,n)=>{let{paramName:a,isOptional:r}=e;if("*"===a){let t=s[n]||"";i=o.slice(0,o.length-t.length).replace(/(.)\/+$/,"$1")}const l=s[n];return t[a]=r&&!l?void 0:(l||"").replace(/%2F/g,"/"),t}),{}),pathname:o,pathnameBase:i,pattern:t}}function he(t){try{return t.split("/").map((t=>decodeURIComponent(t).replace(/\//g,"%2F"))).join("/")}catch(e){return qt(!1,'The URL path "'+t+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+e+")."),t}}function pe(t,e){if("/"===e)return t;if(!t.toLowerCase().startsWith(e.toLowerCase()))return null;let n=e.endsWith("/")?e.length-1:e.length,a=t.charAt(n);return a&&"/"!==a?null:t.slice(n)||"/"}function me(t,e,n,a){return"Cannot include a '"+t+"' character in a manually specified `to."+e+"` field ["+JSON.stringify(a)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function ge(t,e){let n=function(t){return t.filter(((t,e)=>0===e||t.route.path&&t.route.path.length>0))}(t);return e?n.map(((t,e)=>e===n.length-1?t.pathname:t.pathnameBase)):n.map((t=>t.pathnameBase))}function ve(t,e,n,a){let r;void 0===a&&(a=!1),"string"==typeof t?r=Gt(t):(r=zt({},t),Ut(!r.pathname||!r.pathname.includes("?"),me("?","pathname","search",r)),Ut(!r.pathname||!r.pathname.includes("#"),me("#","pathname","hash",r)),Ut(!r.search||!r.search.includes("#"),me("#","search","hash",r)));let o,i=""===t||""===r.pathname,s=i?"/":r.pathname;if(null==s)o=n;else{let t=e.length-1;if(!a&&s.startsWith("..")){let e=s.split("/");for(;".."===e[0];)e.shift(),t-=1;r.pathname=e.join("/")}o=t>=0?e[t]:"/"}let l=function(t,e){void 0===e&&(e="/");let{pathname:n,search:a="",hash:r=""}="string"==typeof t?Gt(t):t,o=n?n.startsWith("/")?n:function(t,e){let n=e.replace(/\/+$/,"").split("/");return t.split("/").forEach((t=>{".."===t?n.length>1&&n.pop():"."!==t&&n.push(t)})),n.length>1?n.join("/"):"/"}(n,e):e;return{pathname:o,search:we(a),hash:xe(r)}}(r,o),c=s&&"/"!==s&&s.endsWith("/"),u=(i||"."===s)&&n.endsWith("/");return l.pathname.endsWith("/")||!c&&!u||(l.pathname+="/"),l}const ye=t=>t.join("/").replace(/\/\/+/g,"/"),be=t=>t.replace(/\/+$/,"").replace(/^\/*/,"/"),we=t=>t&&"?"!==t?t.startsWith("?")?t:"?"+t:"",xe=t=>t&&"#"!==t?t.startsWith("#")?t:"#"+t:"";function Ee(t){return null!=t&&"number"==typeof t.status&&"string"==typeof t.statusText&&"boolean"==typeof t.internal&&"data"in t}const ke=["post","put","patch","delete"];new Set(ke);const Te=["get",...ke];new Set(Te);var Se=function(){return Se=Object.assign||function(t){for(var e,n=1,a=arguments.length;n<a;n++)for(var r in e=arguments[n])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},Se.apply(this,arguments)};function Re(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&e.indexOf(a)<0&&(n[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(t);r<a.length;r++)e.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(t,a[r])&&(n[a[r]]=t[a[r]])}return n}function Ae(t,e,n){if(n||2===arguments.length)for(var a,r=0,o=e.length;r<o;r++)!a&&r in e||(a||(a=Array.prototype.slice.call(e,0,r)),a[r]=e[r]);return t.concat(a||Array.prototype.slice.call(e))}function Pe(t,e){return"function"==typeof t?t(e):t&&(t.current=e),t}"function"==typeof SuppressedError&&SuppressedError;var Ce="undefined"!=typeof window?e.useLayoutEffect:e.useEffect,Le=new WeakMap;function Ne(t,n){var a,r,o,i=(a=null,r=function(e){return t.forEach((function(t){return Pe(t,e)}))},(o=e.useState((function(){return{value:a,callback:r,facade:{get current(){return o.value},set current(t){var e=o.value;e!==t&&(o.value=t,o.callback(t,e))}}}}))[0]).callback=r,o.facade);return Ce((function(){var e=Le.get(i);if(e){var n=new Set(e),a=new Set(t),r=i.current;n.forEach((function(t){a.has(t)||Pe(t,null)})),a.forEach((function(t){n.has(t)||Pe(t,r)}))}Le.set(i,t)}),[t]),i}function Me(t){return t}function Be(t){void 0===t&&(t={});var e=function(t,e){void 0===e&&(e=Me);var n=[],a=!1;return{read:function(){if(a)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:t},useMedium:function(t){var r=e(t,a);return n.push(r),function(){n=n.filter((function(t){return t!==r}))}},assignSyncMedium:function(t){for(a=!0;n.length;){var e=n;n=[],e.forEach(t)}n={push:function(e){return t(e)},filter:function(){return n}}},assignMedium:function(t){a=!0;var e=[];if(n.length){var r=n;n=[],r.forEach(t),e=n}var o=function(){var n=e;e=[],n.forEach(t)},i=function(){return Promise.resolve().then(o)};i(),n={push:function(t){e.push(t),i()},filter:function(t){return e=e.filter(t),n}}}}}(null);return e.options=Se({async:!0,ssr:!1},t),e}var Oe=function(t){var n=t.sideCar,a=Re(t,["sideCar"]);if(!n)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=n.read();if(!r)throw new Error("Sidecar medium not found");return e.createElement(r,Se({},a))};function De(t,e){return t.useMedium(e),Oe}Oe.isSideCarExport=!0;var je=function(){if("undefined"!=typeof __webpack_nonce__)return __webpack_nonce__},Ie=new WeakMap,We=new WeakMap,$e={},_e=0,ze=function(t){return t&&(t.host||ze(t.parentNode))},He=function(t,e,n,a){var r=function(t,e){return e.map((function(e){if(t.contains(e))return e;var n=ze(e);return n&&t.contains(n)?n:null})).filter((function(t){return Boolean(t)}))}(e,Array.isArray(t)?t:[t]);$e[n]||($e[n]=new WeakMap);var o=$e[n],i=[],s=new Set,l=new Set(r),c=function(t){t&&!s.has(t)&&(s.add(t),c(t.parentNode))};r.forEach(c);var u=function(t){t&&!l.has(t)&&Array.prototype.forEach.call(t.children,(function(t){if(s.has(t))u(t);else try{var e=t.getAttribute(a),r=null!==e&&"false"!==e,l=(Ie.get(t)||0)+1,c=(o.get(t)||0)+1;Ie.set(t,l),o.set(t,c),i.push(t),1===l&&r&&We.set(t,!0),1===c&&t.setAttribute(n,"true"),r||t.setAttribute(a,"true")}catch(d){}}))};return u(e),s.clear(),_e++,function(){i.forEach((function(t){var e=Ie.get(t)-1,r=o.get(t)-1;Ie.set(t,e),o.set(t,r),e||(We.has(t)||t.removeAttribute(a),We.delete(t)),r||t.removeAttribute(n)})),--_e||(Ie=new WeakMap,Ie=new WeakMap,We=new WeakMap,$e={})}},Ye=function(t,e,n){void 0===n&&(n="data-aria-hidden");var a=Array.from(Array.isArray(t)?t:[t]),r=function(t){return"undefined"==typeof document?null:(Array.isArray(t)?t[0]:t).ownerDocument.body}(t);return r?(a.push.apply(a,Array.from(r.querySelectorAll("[aria-live], script"))),He(a,r,n,"aria-hidden")):function(){return null}};export{S as $,Ht as A,De as B,Ye as C,v as D,Re as _,Ot as a,jt as b,_t as c,Wt as d,Mt as e,Dt as f,ge as g,It as h,Ut as i,ye as j,pe as k,$t as l,te as m,Ee as n,Bt as o,Gt as p,fe as q,ve as r,o as s,Jt as t,Vt as u,Be as v,Ne as w,Se as x,je as y,Ae as z};
