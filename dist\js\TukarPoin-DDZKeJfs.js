import{f as e,r as a,j as s,al as r,aq as t,at as i,au as n,av as l,aw as o,ax as c,S as d,ak as m}from"./react-vendor-C9kdeAq4.js";import{N as u}from"./NasabahSidebar-Daz452gA.js";import{S as x,e as g}from"./index-BZfOZLOv.js";import{C as h,c as p,B as j,a as b,b as y}from"./card-l7WlC16I.js";import{B as k}from"./badge-8i3m94Oh.js";import{I as f}from"./input-B25hGi49.js";import{C as N}from"./ConfirmDialog-KlxVkBeS.js";import"./radix-vendor-CttiZxwU.js";import"./vendor-C0DTsUaw.js";import"./utils-vendor-DeZn2tlB.js";import"./query-vendor-B0Wv6VB8.js";const v=()=>{const v=e(),[w,S]=a.useState(!0),[_,P]=a.useState(1250),[T,C]=a.useState(""),[q,B]=a.useState("all"),[L,R]=a.useState("catalog"),[V,$]=a.useState(null),[A,I]=a.useState(!1),[D,E]=a.useState(!1),[F]=a.useState([{id:"1",name:"Pulsa Rp 10.000",description:"Pulsa untuk semua operator",points_required:100,category:"pulsa",stock:50,is_available:!0},{id:"2",name:"Pulsa Rp 25.000",description:"Pulsa untuk semua operator",points_required:250,category:"pulsa",stock:30,is_available:!0},{id:"3",name:"Voucher Belanja Rp 50.000",description:"Voucher belanja di minimarket",points_required:500,category:"voucher",stock:20,is_available:!0},{id:"4",name:"Voucher Grab Food Rp 30.000",description:"Voucher makanan online",points_required:300,category:"food",stock:15,is_available:!0},{id:"5",name:"Power Bank 10.000mAh",description:"Power bank portable berkualitas",points_required:800,category:"electronics",stock:5,is_available:!0},{id:"6",name:"Voucher Steam Rp 100.000",description:"Voucher game Steam",points_required:1e3,category:"other",stock:10,is_available:!0},{id:"7",name:"Tumbler Stainless Steel",description:"Tumbler ramah lingkungan 500ml",points_required:600,category:"other",stock:0,is_available:!1}]),[M]=a.useState([{id:"EXC001",reward_name:"Pulsa Rp 10.000",points_used:100,date:"2024-06-12",status:"completed"},{id:"EXC002",reward_name:"Voucher Belanja Rp 25.000",points_used:250,date:"2024-06-08",status:"completed"}]),G=[{id:"all",name:"Semua",icon:r},{id:"pulsa",name:"Pulsa",icon:i},{id:"voucher",name:"Voucher",icon:n},{id:"electronics",name:"Elektronik",icon:l},{id:"food",name:"Makanan",icon:o},{id:"other",name:"Lainnya",icon:c}];a.useEffect((()=>{(async()=>{S(!0),await new Promise((e=>setTimeout(e,1e3)));const e=localStorage.getItem("user");if(!e)return void v("/login");if("nasabah"!==JSON.parse(e).role)return void v("/login");const a=localStorage.getItem("userPoints");a&&P(parseInt(a)),S(!1)})()}),[v]);const K=e=>{switch(e){case"pulsa":return"bg-blue-100 text-blue-600";case"voucher":return"bg-green-100 text-green-600";case"electronics":return"bg-purple-100 text-purple-600";case"food":return"bg-orange-100 text-orange-600";default:return"bg-gray-100 text-gray-600"}},O=F.filter((e=>{const a=e.name.toLowerCase().includes(T.toLowerCase())||e.description.toLowerCase().includes(T.toLowerCase()),s="all"===q||e.category===q;return a&&s})),X=e=>{switch(e){case"completed":return"bg-green-100 text-green-800 border-green-200";case"pending":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"cancelled":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},H=e=>{switch(e){case"completed":return"Selesai";case"pending":return"Menunggu";case"cancelled":return"Dibatalkan";default:return e}};return w?s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx(u,{}),s.jsx("div",{className:"lg:ml-64",children:s.jsx("main",{className:"p-4 pt-16 lg:pt-8",children:s.jsx(x,{type:"tukar-poin"})})})]}):s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx(u,{}),s.jsx("div",{className:"lg:ml-64",children:s.jsxs("main",{className:"p-4 pt-16 lg:pt-8 space-y-6",children:[s.jsxs("div",{children:[s.jsxs("h1",{className:"text-2xl lg:text-3xl font-bold text-gray-800 flex items-center",children:[s.jsx(r,{className:"w-8 h-8 mr-3 text-bank-green-600"}),"Tukar Poin"]}),s.jsx("p",{className:"text-gray-600 mt-1",children:"Tukarkan poin Anda dengan berbagai hadiah menarik"})]}),s.jsxs("div",{className:"flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit",children:[s.jsx("button",{onClick:()=>R("catalog"),className:"px-4 py-2 rounded-md text-sm font-medium transition-colors "+("catalog"===L?"bg-white text-bank-green-700 shadow-sm":"text-gray-600 hover:text-gray-800"),children:"Katalog Hadiah"}),s.jsx("button",{onClick:()=>R("history"),className:"px-4 py-2 rounded-md text-sm font-medium transition-colors "+("history"===L?"bg-white text-bank-green-700 shadow-sm":"text-gray-600 hover:text-gray-800"),children:"Riwayat Tukar"})]}),"catalog"===L?s.jsxs(s.Fragment,{children:[s.jsx(h,{children:s.jsx(p,{className:"p-4",children:s.jsxs("div",{className:"flex flex-col lg:flex-row gap-4",children:[s.jsx("div",{className:"flex-1",children:s.jsxs("div",{className:"relative",children:[s.jsx(t,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),s.jsx(f,{placeholder:"Cari hadiah...",value:T,onChange:e=>C(e.target.value),className:"pl-10"})]})}),s.jsx("div",{className:"flex gap-2 overflow-x-auto pb-2",children:G.map((e=>{const a=e.icon;return s.jsxs("button",{onClick:()=>B(e.id),className:"flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-colors "+(q===e.id?"bg-bank-green-100 text-bank-green-700 border border-bank-green-200":"bg-white text-gray-600 hover:bg-gray-50 border border-gray-200"),children:[s.jsx(a,{className:"w-4 h-4"}),s.jsx("span",{children:e.name})]},e.id)}))})]})})}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:O.map((e=>{const a=(e=>{const a=G.find((a=>a.id===e));return a?a.icon:r})(e.category),t=_>=e.points_required,i=e.is_available&&e.stock>0;return s.jsx(h,{className:"hover:shadow-lg transition-all duration-300 "+(i?"":"opacity-60"),children:s.jsxs(p,{className:"p-6",children:[s.jsxs("div",{className:"flex items-start justify-between mb-4",children:[s.jsx("div",{className:`w-12 h-12 rounded-full flex items-center justify-center ${K(e.category)}`,children:s.jsx(a,{className:"w-6 h-6"})}),s.jsxs("div",{className:"text-right",children:[s.jsx("p",{className:"text-2xl font-bold text-bank-green-600",children:e.points_required.toLocaleString()}),s.jsx("p",{className:"text-sm text-gray-500",children:"poin"})]})]}),s.jsxs("div",{className:"space-y-2 mb-4",children:[s.jsx("h3",{className:"font-bold text-gray-800",children:e.name}),s.jsx("p",{className:"text-sm text-gray-600",children:e.description}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs(k,{variant:"outline",className:"text-xs",children:["Stok: ",e.stock]}),!i&&s.jsx(k,{className:"bg-red-100 text-red-800 border-red-200 text-xs",children:"Tidak Tersedia"})]})]}),s.jsx(j,{onClick:()=>(e=>{_<e.points_required?g.error("Poin Tidak Cukup",{description:`Anda membutuhkan ${e.points_required} poin, tetapi hanya memiliki ${_} poin`}):e.is_available&&0!==e.stock?($(e),I(!0)):g.error("Item Tidak Tersedia",{description:"Maaf, item ini sedang tidak tersedia"})})(e),disabled:!t||!i||D,className:"w-full hover-scale "+(t&&i?"bg-bank-green-600 hover:bg-bank-green-700 text-white":"bg-gray-200 text-gray-500 cursor-not-allowed"),children:i?t?s.jsxs(s.Fragment,{children:[s.jsx(d,{className:"w-4 h-4 mr-2"}),"Tukar Sekarang"]}):`Butuh ${(e.points_required-_).toLocaleString()} poin lagi`:"Tidak Tersedia"})]})},e.id)}))}),0===O.length&&s.jsx(h,{children:s.jsxs(p,{className:"p-12 text-center",children:[s.jsx(r,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),s.jsx("h3",{className:"text-lg font-medium text-gray-600 mb-2",children:"Tidak ada hadiah"}),s.jsx("p",{className:"text-gray-500",children:"Tidak ada hadiah yang sesuai dengan pencarian Anda"})]})})]}):s.jsxs(h,{children:[s.jsx(b,{children:s.jsxs(y,{className:"flex items-center",children:[s.jsx(m,{className:"w-5 h-5 mr-2 text-blue-600"}),"Riwayat Penukaran Poin"]})}),s.jsx(p,{children:0===M.length?s.jsxs("div",{className:"text-center py-12",children:[s.jsx(m,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),s.jsx("h3",{className:"text-lg font-medium text-gray-600 mb-2",children:"Belum ada riwayat"}),s.jsx("p",{className:"text-gray-500",children:"Anda belum pernah menukar poin"})]}):s.jsx("div",{className:"space-y-4",children:M.map((e=>{return s.jsxs("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-200",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center",children:s.jsx(r,{className:"w-6 h-6 text-purple-600"})}),s.jsxs("div",{children:[s.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[s.jsx("h4",{className:"font-medium text-gray-800",children:e.reward_name}),s.jsx(k,{className:X(e.status),children:H(e.status)})]}),s.jsxs("p",{className:"text-sm text-gray-500",children:[e.id," • ",(a=e.date,new Date(a).toLocaleDateString("id-ID",{day:"numeric",month:"long",year:"numeric"}))]})]})]}),s.jsx("div",{className:"text-right",children:s.jsxs("p",{className:"text-lg font-bold text-red-600",children:["-",e.points_used.toLocaleString()," Poin"]})})]},e.id);var a}))})})]})]})}),s.jsx(N,{isOpen:A,onClose:()=>I(!1),onConfirm:async()=>{if(V){E(!0),I(!1);try{await new Promise((e=>setTimeout(e,2e3)));const e=_-V.points_required;P(e),localStorage.setItem("userPoints",e.toString()),g.success("Penukaran Berhasil!",{description:`${V.name} berhasil ditukar dengan ${V.points_required} poin`}),R("history")}catch(e){g.error("Penukaran Gagal",{description:"Terjadi kesalahan saat menukar poin. Silakan coba lagi."})}finally{E(!1),$(null)}}},title:"Konfirmasi Penukaran Poin",description:V?`Apakah Anda yakin ingin menukar ${V.points_required} poin dengan ${V.name}?`:"",confirmText:"Tukar Sekarang",cancelText:"Batal",type:"warning"})]})};export{v as default};
