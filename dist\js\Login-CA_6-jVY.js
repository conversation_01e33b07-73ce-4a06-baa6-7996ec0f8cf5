import{f as e,r as s,j as a,m as r,g as t,E as l,n}from"./react-vendor-C9kdeAq4.js";import{B as i,C as m,a as d,b as c,d as o,c as x}from"./card-l7WlC16I.js";import{I as h}from"./input-B25hGi49.js";import{L as p}from"./label-ZSTvXwUz.js";import{C as u}from"./checkbox-BPqGXD6n.js";import"./radix-vendor-CttiZxwU.js";import"./vendor-C0DTsUaw.js";import"./utils-vendor-DeZn2tlB.js";import"./index-BZfOZLOv.js";import"./query-vendor-B0Wv6VB8.js";const j=()=>{const j=e(),[b,g]=s.useState({email:"",password:"",rememberMe:!1}),[f,N]=s.useState(!1),[w,y]=s.useState(!1),v=[{email:"<EMAIL>",password:"password",role:"admin"},{email:"<EMAIL>",password:"password",role:"nasabah"}],k=e=>{const{name:s,value:a}=e.target;g((e=>({...e,[s]:a})))};return a.jsx("div",{className:"min-h-screen bg-gradient-to-br from-bank-green-50 to-bank-blue-50 flex items-center justify-center p-4",children:a.jsxs("div",{className:"w-full max-w-md",children:[a.jsxs(i,{variant:"ghost",onClick:()=>j("/"),className:"mb-6 hover:bg-white/50",children:[a.jsx(r,{className:"w-4 h-4 mr-2"}),"Kembali ke Beranda"]}),a.jsxs(m,{className:"w-full shadow-2xl border-0 bg-white/90 backdrop-blur-sm animate-bounce-in",children:[a.jsxs(d,{className:"text-center space-y-4",children:[a.jsx("div",{className:"w-16 h-16 mx-auto bg-gradient-green rounded-2xl flex items-center justify-center",children:a.jsx(t,{className:"w-8 h-8 text-white"})}),a.jsxs("div",{children:[a.jsx(c,{className:"text-2xl font-bold text-gray-800",children:"Masuk ke Akun"}),a.jsx(o,{className:"text-gray-600",children:"Masuk untuk mengakses Bank Sampah Digital"})]})]}),a.jsxs(x,{className:"space-y-6",children:[a.jsxs("form",{onSubmit:async e=>{e.preventDefault(),y(!0),await new Promise((e=>setTimeout(e,1e3)));const s=v.find((e=>e.email===b.email&&e.password===b.password));s&&(localStorage.setItem("user",JSON.stringify(s)),"admin"===s.role?j("/admin/dashboard"):j("/nasabah/dashboard")),y(!1)},className:"space-y-4",children:[a.jsxs("div",{className:"space-y-2",children:[a.jsx(p,{htmlFor:"email",className:"text-sm font-medium text-gray-700",children:"Email"}),a.jsx(h,{id:"email",name:"email",type:"email",placeholder:"<EMAIL>",value:b.email,onChange:k,className:"w-full transition-all duration-200 focus:ring-2 focus:ring-bank-green-500",required:!0})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(p,{htmlFor:"password",className:"text-sm font-medium text-gray-700",children:"Password"}),a.jsxs("div",{className:"relative",children:[a.jsx(h,{id:"password",name:"password",type:f?"text":"password",placeholder:"Masukkan password",value:b.password,onChange:k,className:"w-full pr-10 transition-all duration-200 focus:ring-2 focus:ring-bank-green-500",required:!0}),a.jsx("button",{type:"button",onClick:()=>N(!f),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700",children:f?a.jsx(l,{className:"w-4 h-4"}):a.jsx(n,{className:"w-4 h-4"})})]})]}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(u,{id:"rememberMe",checked:b.rememberMe,onCheckedChange:e=>g((s=>({...s,rememberMe:!!e})))}),a.jsx(p,{htmlFor:"rememberMe",className:"text-sm text-gray-600",children:"Ingat saya"})]}),a.jsx(i,{type:"submit",disabled:w,className:"w-full btn-primary py-3 text-base font-medium",children:w?"Memproses...":"Masuk"})]}),a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-0 flex items-center",children:a.jsx("span",{className:"w-full border-t border-gray-300"})}),a.jsx("div",{className:"relative flex justify-center text-xs uppercase",children:a.jsx("span",{className:"bg-white px-2 text-gray-500",children:"atau"})})]}),a.jsx("div",{className:"text-center space-y-2",children:a.jsxs("p",{className:"text-sm text-gray-600",children:["Belum punya akun?"," ",a.jsx("button",{onClick:()=>j("/register"),className:"text-bank-green-600 hover:text-bank-green-700 font-medium hover:underline transition-colors",children:"Daftar di sini"})]})}),a.jsxs("div",{className:"text-xs text-gray-500 bg-gray-50 p-3 rounded-lg",children:[a.jsx("p",{className:"font-medium mb-1",children:"Demo Credentials:"}),a.jsx("p",{children:"Admin: <EMAIL> / password"}),a.jsx("p",{children:"Nasabah: <EMAIL> / password"})]})]})]})]})})};export{j as default};
