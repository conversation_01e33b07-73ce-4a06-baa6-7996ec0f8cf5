import{f as e,r as a,j as s,U as t,y as n,h as r,z as l,F as i,l as d,k as o,i as c,G as m,H as x}from"./react-vendor-C9kdeAq4.js";import{C as h,c as p,a as u,b as g,d as j,B as b}from"./card-l7WlC16I.js";import{I as f}from"./input-B25hGi49.js";import{L as N}from"./label-ZSTvXwUz.js";import{T as y,a as v,b as k,c as w,d as S,e as T}from"./table-qPaxNgV0.js";import{D as C,a as D,b as _,c as A,d as B,e as M,C as X}from"./ConfirmDialog-KlxVkBeS.js";import{e as P,S as F}from"./index-BZfOZLOv.js";import{A as q}from"./AdminSidebar-y32hu_7f.js";import"./radix-vendor-CttiZxwU.js";import"./vendor-C0DTsUaw.js";import"./utils-vendor-DeZn2tlB.js";import"./query-vendor-B0Wv6VB8.js";const E=()=>{const E=e(),[I,H]=a.useState([]),[J,L]=a.useState(!0),[O,$]=a.useState(null),[K,z]=a.useState({name:"",email:"",phone:"",address:""}),[G,U]=a.useState(!1),[Y,R]=a.useState("add"),[W,Q]=a.useState(null),V=[{id:1,name:"Siti Nurhaliza",email:"<EMAIL>",phone:"081234567890",address:"Jl. Merdeka No. 123, Jakarta",total_points:1250,created_at:"2024-01-15"},{id:2,name:"Budi Santoso",email:"<EMAIL>",phone:"081234567891",address:"Jl. Sudirman No. 456, Bandung",total_points:890,created_at:"2024-02-10"},{id:3,name:"Ahmad Wijaya",email:"<EMAIL>",phone:"081234567892",address:"Jl. Gatot Subroto No. 789, Surabaya",total_points:2100,created_at:"2024-03-05"}];a.useEffect((()=>{const e=localStorage.getItem("user");if(!e)return void E("/login");if("admin"!==JSON.parse(e).role)return P.error("Akses Ditolak",{description:"Anda tidak memiliki akses ke halaman admin"}),void E("/");setTimeout((()=>{H(V),L(!1)}),1500)}),[E]);const Z=e=>{const{name:a,value:s}=e.target;z((e=>({...e,[a]:s})))};if(J)return s.jsxs("div",{className:"min-h-screen bg-gray-50 flex",children:[s.jsx(q,{}),s.jsx("div",{className:"flex-1 lg:ml-0 p-4 lg:p-8 pt-16 lg:pt-8",children:s.jsx(F,{type:"table"})})]});const ee={total:I.length,totalPoints:I.reduce(((e,a)=>e+a.total_points),0),avgPoints:I.length>0?Math.round(I.reduce(((e,a)=>e+a.total_points),0)/I.length):0,activeThisMonth:I.filter((e=>{const a=new Date(e.created_at),s=new Date;return a.getMonth()===s.getMonth()&&a.getFullYear()===s.getFullYear()})).length};return s.jsxs("div",{className:"min-h-screen bg-gray-50 flex",children:[s.jsx(q,{}),s.jsx("div",{className:"flex-1 lg:ml-0",children:s.jsxs("main",{className:"p-4 lg:p-8",children:[s.jsxs("div",{className:"mb-8",children:[s.jsxs("h2",{className:"text-2xl font-bold text-gray-800 mb-2 flex items-center",children:[s.jsx(t,{className:"w-6 h-6 mr-3 text-bank-green-600"}),"Kelola Nasabah"]}),s.jsx("p",{className:"text-gray-600",children:"Kelola data nasabah Bank Sampah Digital"})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[s.jsx(h,{className:"hover:shadow-lg transition-shadow duration-300",children:s.jsx(p,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Total Nasabah"}),s.jsx("p",{className:"text-2xl font-bold text-gray-900",children:ee.total})]}),s.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-bank-green-100 to-bank-green-200 rounded-xl flex items-center justify-center",children:s.jsx(t,{className:"w-6 h-6 text-bank-green-600"})})]})})}),s.jsx(h,{className:"hover:shadow-lg transition-shadow duration-300",children:s.jsx(p,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Total Poin"}),s.jsx("p",{className:"text-2xl font-bold text-purple-600",children:ee.totalPoints.toLocaleString()})]}),s.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-purple-100 to-purple-200 rounded-xl flex items-center justify-center",children:s.jsx(n,{className:"w-6 h-6 text-purple-600"})})]})})}),s.jsx(h,{className:"hover:shadow-lg transition-shadow duration-300",children:s.jsx(p,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Rata-rata Poin"}),s.jsx("p",{className:"text-2xl font-bold text-blue-600",children:ee.avgPoints})]}),s.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-100 to-blue-200 rounded-xl flex items-center justify-center",children:s.jsx(r,{className:"w-6 h-6 text-blue-600"})})]})})}),s.jsx(h,{className:"hover:shadow-lg transition-shadow duration-300",children:s.jsx(p,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Baru Bulan Ini"}),s.jsx("p",{className:"text-2xl font-bold text-orange-600",children:ee.activeThisMonth})]}),s.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-orange-100 to-orange-200 rounded-xl flex items-center justify-center",children:s.jsx(l,{className:"w-6 h-6 text-orange-600"})})]})})})]}),s.jsxs(h,{className:"shadow-xl border-0 bg-gradient-to-br from-white to-gray-50",children:[s.jsx(u,{className:"bg-gradient-to-r from-bank-green-50 to-bank-blue-50 rounded-t-lg border-b border-gray-100",children:s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsxs("div",{children:[s.jsxs(g,{className:"text-2xl font-bold text-gray-800 flex items-center gap-3",children:[s.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-bank-green-500 to-bank-green-600 rounded-xl flex items-center justify-center",children:s.jsx(t,{className:"w-5 h-5 text-white"})}),"Daftar Nasabah"]}),s.jsx(j,{className:"text-gray-600 mt-2",children:"Kelola dan pantau data nasabah Bank Sampah Digital"})]}),s.jsxs("button",{onClick:()=>{R("add"),z({name:"",email:"",phone:"",address:""}),U(!0)},className:"btn-add group",children:[s.jsx(i,{className:"w-5 h-5 group-hover:rotate-90 transition-transform duration-300"}),"Tambah Nasabah"]})]})}),s.jsx(p,{className:"p-0",children:s.jsxs(y,{children:[s.jsx(v,{children:s.jsxs(k,{className:"bg-gray-50/50",children:[s.jsx(w,{className:"font-semibold text-gray-700",children:"Nama"}),s.jsx(w,{className:"font-semibold text-gray-700",children:"Email"}),s.jsx(w,{className:"font-semibold text-gray-700",children:"Telepon"}),s.jsx(w,{className:"font-semibold text-gray-700",children:"Alamat"}),s.jsx(w,{className:"font-semibold text-gray-700",children:"Total Poin"}),s.jsx(w,{className:"font-semibold text-gray-700",children:"Bergabung"}),s.jsx(w,{className:"font-semibold text-gray-700",children:"Aksi"})]})}),s.jsx(S,{children:I.map((e=>s.jsxs(k,{className:"hover:bg-gray-50/50 transition-colors",children:[s.jsx(T,{className:"font-medium text-gray-800",children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-bank-green-100 to-bank-green-200 rounded-full flex items-center justify-center",children:s.jsx("span",{className:"text-bank-green-700 font-semibold text-sm",children:e.name.charAt(0).toUpperCase()})}),e.name]})}),s.jsx(T,{className:"text-gray-600",children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(d,{className:"w-4 h-4 text-gray-400"}),e.email]})}),s.jsx(T,{className:"text-gray-600",children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(o,{className:"w-4 h-4 text-gray-400"}),e.phone]})}),s.jsx(T,{className:"text-gray-600 max-w-xs truncate",title:e.address,children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(c,{className:"w-4 h-4 text-gray-400"}),e.address]})}),s.jsx(T,{children:s.jsxs("span",{className:"bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800 px-3 py-1.5 rounded-full text-sm font-semibold shadow-sm flex items-center gap-1 w-fit",children:[s.jsx(n,{className:"w-3 h-3"}),e.total_points," poin"]})}),s.jsx(T,{className:"text-gray-600",children:new Date(e.created_at).toLocaleDateString("id-ID")}),s.jsx(T,{children:s.jsxs("div",{className:"flex space-x-2",children:[s.jsxs("button",{className:"btn-edit group flex items-center gap-2 px-3 py-2 text-sm rounded-lg",onClick:()=>(e=>{R("edit"),$(e),z({name:e.name,email:e.email,phone:e.phone,address:e.address}),U(!0)})(e),title:"Edit Nasabah",children:[s.jsx(m,{className:"w-4 h-4 group-hover:scale-110 transition-transform duration-200"}),"Edit"]}),s.jsxs("button",{className:"btn-delete group flex items-center gap-2 px-3 py-2 text-sm rounded-lg",onClick:()=>(e=>{Q(e)})(e),title:"Hapus Nasabah",children:[s.jsx(x,{className:"w-4 h-4 group-hover:scale-110 transition-transform duration-200"}),"Hapus"]})]})})]},e.id)))})]})})]})]})}),s.jsx(C,{open:G,onOpenChange:U,children:s.jsxs(D,{children:[s.jsxs(_,{children:[s.jsx(A,{children:"add"===Y?"Tambah Nasabah Baru":"Edit Data Nasabah"}),s.jsx(B,{children:"add"===Y?"Masukkan data nasabah baru":"Perbarui data nasabah"})]}),s.jsxs("form",{onSubmit:e=>{if(e.preventDefault(),"add"===Y){const e={id:Math.max(...I.map((e=>e.id)))+1,...K,total_points:0,created_at:(new Date).toISOString().split("T")[0]};H((a=>[...a,e])),P.success("Nasabah baru berhasil ditambahkan!",{description:`${K.name} telah terdaftar sebagai nasabah`})}else if(O){const e={...O,...K};H((a=>a.map((a=>a.id===O.id?e:a)))),P.success("Data nasabah berhasil diperbarui!",{description:`Informasi ${K.name} telah diperbaharui`})}U(!1),$(null)},className:"space-y-4",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsx(N,{htmlFor:"name",children:"Nama Lengkap"}),s.jsx(f,{id:"name",name:"name",value:K.name,onChange:Z,placeholder:"Masukkan nama lengkap",required:!0})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(N,{htmlFor:"email",children:"Email"}),s.jsx(f,{id:"email",name:"email",type:"email",value:K.email,onChange:Z,placeholder:"<EMAIL>",required:!0})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(N,{htmlFor:"phone",children:"No. Telepon"}),s.jsx(f,{id:"phone",name:"phone",value:K.phone,onChange:Z,placeholder:"08XXXXXXXXXX",required:!0})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(N,{htmlFor:"address",children:"Alamat"}),s.jsx("textarea",{id:"address",name:"address",value:K.address,onChange:Z,placeholder:"Masukkan alamat lengkap",className:"w-full px-3 py-2 border border-input bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 min-h-[80px] rounded-md",required:!0})]}),s.jsxs(M,{children:[s.jsx(b,{type:"button",variant:"outline",onClick:()=>U(!1),children:"Batal"}),s.jsx(b,{type:"submit",className:"btn-primary",children:"add"===Y?"Tambah":"Simpan"})]})]})]})}),s.jsx(X,{isOpen:!!W,onClose:()=>Q(null),onConfirm:()=>{W&&(H((e=>e.filter((e=>e.id!==W.id)))),P.success("Nasabah berhasil dihapus!",{description:`${W.name} telah dihapus dari sistem`}),Q(null))},title:"Hapus Nasabah",description:`Apakah Anda yakin ingin menghapus nasabah ${null==W?void 0:W.name}? Semua data transaksi dan poin yang terkait akan ikut terhapus. Tindakan ini tidak dapat dibatalkan.`,confirmText:"Hapus",cancelText:"Batal",type:"danger"})]})};export{E as default};
