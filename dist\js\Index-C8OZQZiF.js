import{f as e,r as a,j as s,g as n,X as t,M as i,U as l,h as r,L as c,i as d,k as x,l as m}from"./react-vendor-C9kdeAq4.js";import{B as o,C as h,a as g,b as u,c as j,d as b}from"./card-l7WlC16I.js";import{B as f}from"./badge-8i3m94Oh.js";import{e as p}from"./index-BZfOZLOv.js";import"./radix-vendor-CttiZxwU.js";import"./vendor-C0DTsUaw.js";import"./utils-vendor-DeZn2tlB.js";import"./query-vendor-B0Wv6VB8.js";const k=()=>{const k=e(),[y,N]=a.useState(!1),[v,w]=a.useState(!1);a.useEffect((()=>{const e=()=>{N(window.scrollY>50)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)}),[]);const S=[{icon:l,label:"Nasabah Aktif",value:"2,500+",color:"text-bank-blue-500"},{icon:n,label:"Sampah Terkumpul",value:"15 Ton",color:"text-bank-green-600"},{icon:r,label:"Poin Dibagikan",value:"50,000+",color:"text-purple-500"},{icon:c,label:"CO₂ Dikurangi",value:"8.2 Ton",color:"text-emerald-500"}],D=[{icon:n,title:"Tukar Sampah Jadi Poin",description:"Kumpulkan sampah daur ulang dan tukarkan dengan poin reward yang bisa digunakan untuk berbagai kebutuhan."},{icon:d,title:"Jemput Sampah",description:"Layanan jemput sampah langsung ke rumah Anda dengan jadwal yang fleksibel dan mudah."},{icon:r,title:"Tracking Real-time",description:"Pantau perkembangan sampah yang sudah dikumpulkan dan poin yang sudah didapatkan secara real-time."}];return s.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-bank-green-50 to-bank-blue-50",children:[s.jsxs("nav",{className:"fixed top-0 w-full z-50 transition-all duration-300 "+(y?"bg-white/90 backdrop-blur-md shadow-lg py-2":"bg-transparent py-4"),children:[s.jsxs("div",{className:"container mx-auto px-4 flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-10 h-10 bg-gradient-green rounded-xl flex items-center justify-center",children:s.jsx(n,{className:"w-6 h-6 text-white"})}),s.jsx("span",{className:"text-xl font-bold text-gray-800",children:"Bank Sampah Digital"})]}),s.jsxs("div",{className:"hidden md:flex items-center space-x-8",children:[s.jsx("a",{href:"#home",className:"text-gray-700 hover:text-bank-green-600 transition-colors",children:"Beranda"}),s.jsx("a",{href:"#features",className:"text-gray-700 hover:text-bank-green-600 transition-colors",children:"Fitur"}),s.jsx("a",{href:"#about",className:"text-gray-700 hover:text-bank-green-600 transition-colors",children:"Tentang"}),s.jsx("a",{href:"#contact",className:"text-gray-700 hover:text-bank-green-600 transition-colors",children:"Kontak"}),s.jsx(o,{onClick:()=>k("/login"),className:"btn-primary",children:"Masuk"})]}),s.jsx("button",{className:"md:hidden p-2",onClick:()=>w(!v),children:v?s.jsx(t,{className:"w-6 h-6"}):s.jsx(i,{className:"w-6 h-6"})})]}),v&&s.jsx("div",{className:"md:hidden absolute top-full left-0 w-full bg-white shadow-lg animate-slide-in-right",children:s.jsxs("div",{className:"p-4 space-y-4",children:[s.jsx("a",{href:"#home",className:"block text-gray-700 hover:text-bank-green-600 transition-colors",children:"Beranda"}),s.jsx("a",{href:"#features",className:"block text-gray-700 hover:text-bank-green-600 transition-colors",children:"Fitur"}),s.jsx("a",{href:"#about",className:"block text-gray-700 hover:text-bank-green-600 transition-colors",children:"Tentang"}),s.jsx("a",{href:"#contact",className:"block text-gray-700 hover:text-bank-green-600 transition-colors",children:"Kontak"}),s.jsx(o,{onClick:()=>k("/login"),className:"w-full btn-primary",children:"Masuk"})]})})]}),s.jsx("section",{id:"home",className:"pt-24 pb-20 px-4",children:s.jsx("div",{className:"container mx-auto text-center",children:s.jsxs("div",{className:"animate-fade-in",children:[s.jsx(f,{className:"mb-6 bg-bank-green-100 text-bank-green-800 hover:bg-bank-green-200",children:"🌱 Platform Digital Ramah Lingkungan"}),s.jsxs("h1",{className:"text-4xl md:text-6xl font-bold text-gray-800 mb-6 leading-tight",children:["Tukar Sampah Jadi",s.jsx("span",{className:"bg-gradient-to-r from-bank-green-600 to-bank-blue-600 bg-clip-text text-transparent",children:" Berkah"})]}),s.jsx("p",{className:"text-xl text-gray-600 mb-8 max-w-2xl mx-auto",children:"Platform digital yang memudahkan Anda mengumpulkan sampah daur ulang dan menukarkannya dengan poin reward. Mari bersama-sama menjaga lingkungan!"}),s.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[s.jsx(o,{onClick:()=>{p({title:"Selamat Datang!",description:"Silakan login atau daftar untuk memulai"}),k("/login")},className:"btn-primary text-lg px-8 py-3 smooth-bounce",children:"Mulai Sekarang"}),s.jsx(o,{onClick:()=>k("/register"),className:"btn-secondary text-lg px-8 py-3",children:"Daftar Gratis"})]})]})})}),s.jsx("section",{className:"py-16 px-4 bg-white",children:s.jsx("div",{className:"container mx-auto",children:s.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-8",children:S.map(((e,a)=>s.jsxs("div",{className:"text-center hover-scale",children:[s.jsx("div",{className:`w-16 h-16 mx-auto mb-4 rounded-full bg-gray-50 flex items-center justify-center ${e.color}`,children:s.jsx(e.icon,{className:"w-8 h-8"})}),s.jsx("div",{className:"text-2xl font-bold text-gray-800 mb-2",children:e.value}),s.jsx("div",{className:"text-gray-600",children:e.label})]},a)))})})}),s.jsx("section",{id:"features",className:"py-20 px-4",children:s.jsxs("div",{className:"container mx-auto",children:[s.jsxs("div",{className:"text-center mb-16",children:[s.jsx(f,{className:"mb-4 bg-bank-blue-100 text-bank-blue-800",children:"Fitur Unggulan"}),s.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-800 mb-4",children:"Kenapa Pilih Bank Sampah Digital?"}),s.jsx("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Kami menyediakan solusi lengkap untuk pengelolaan sampah yang mudah, efisien, dan menguntungkan."})]}),s.jsx("div",{className:"grid md:grid-cols-3 gap-8",children:D.map(((e,a)=>s.jsxs(h,{className:"hover-scale border-0 shadow-lg glass-effect",children:[s.jsxs(g,{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 mx-auto mb-4 gradient-green rounded-2xl flex items-center justify-center",children:s.jsx(e.icon,{className:"w-8 h-8 text-white"})}),s.jsx(u,{className:"text-xl",children:e.title})]}),s.jsx(j,{children:s.jsx(b,{className:"text-center text-gray-600",children:e.description})})]},a)))})]})}),s.jsx("section",{id:"about",className:"py-20 px-4 bg-white",children:s.jsx("div",{className:"container mx-auto",children:s.jsxs("div",{className:"grid md:grid-cols-2 gap-12 items-center",children:[s.jsxs("div",{children:[s.jsx(f,{className:"mb-4 bg-bank-green-100 text-bank-green-800",children:"Tentang Kami"}),s.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-800 mb-6",children:"Bersama Membangun Masa Depan yang Berkelanjutan"}),s.jsx("p",{className:"text-gray-600 mb-6",children:"Bank Sampah Digital adalah platform inovatif yang menghubungkan komunitas dengan sistem pengelolaan sampah yang efisien. Kami percaya bahwa setiap sampah memiliki nilai dan dapat diubah menjadi sesuatu yang bermanfaat."}),s.jsx("p",{className:"text-gray-600 mb-8",children:"Dengan teknologi modern dan pendekatan yang user-friendly, kami memudahkan masyarakat untuk berpartisipasi dalam gerakan lingkungan sambil mendapatkan keuntungan ekonomis."}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-6 h-6 rounded-full bg-bank-green-600 flex items-center justify-center",children:s.jsx("span",{className:"w-2 h-2 bg-white rounded-full"})}),s.jsx("span",{className:"text-gray-700",children:"Proses yang mudah dan transparan"})]}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-6 h-6 rounded-full bg-bank-green-600 flex items-center justify-center",children:s.jsx("span",{className:"w-2 h-2 bg-white rounded-full"})}),s.jsx("span",{className:"text-gray-700",children:"Reward yang menarik dan berguna"})]}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-6 h-6 rounded-full bg-bank-green-600 flex items-center justify-center",children:s.jsx("span",{className:"w-2 h-2 bg-white rounded-full"})}),s.jsx("span",{className:"text-gray-700",children:"Dampak positif untuk lingkungan"})]})]})]}),s.jsx("div",{className:"relative",children:s.jsx("div",{className:"w-full h-96 flex items-center justify-center",children:s.jsxs("svg",{viewBox:"0 0 400 300",className:"w-full h-full max-w-md",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[s.jsx("circle",{cx:"200",cy:"150",r:"140",fill:"#f0fdf4",opacity:"0.8"}),s.jsxs("g",{transform:"translate(200,150)",children:[s.jsx("circle",{cx:"0",cy:"0",r:"35",fill:"#22c55e",opacity:"0.1"}),s.jsx("path",{d:"M-15 -8 L15 -8 L8 -20 M15 -8 L0 17 L-12 5 M0 17 L-15 -8 L-3 -16",stroke:"#16a34a",strokeWidth:"3",fill:"none",strokeLinecap:"round",strokeLinejoin:"round"})]}),s.jsxs("g",{transform:"translate(120,200)",children:[s.jsx("rect",{x:"-15",y:"-20",width:"30",height:"35",rx:"3",fill:"#3b82f6"}),s.jsx("rect",{x:"-12",y:"-17",width:"24",height:"3",fill:"#2563eb"}),s.jsx("text",{x:"0",y:"0",textAnchor:"middle",fontSize:"8",fill:"white",fontWeight:"bold",children:"♻"})]}),s.jsxs("g",{transform:"translate(280,200)",children:[s.jsx("rect",{x:"-15",y:"-20",width:"30",height:"35",rx:"3",fill:"#16a34a"}),s.jsx("rect",{x:"-12",y:"-17",width:"24",height:"3",fill:"#15803d"}),s.jsx("text",{x:"0",y:"0",textAnchor:"middle",fontSize:"8",fill:"white",fontWeight:"bold",children:"🗑"})]}),s.jsxs("g",{transform:"translate(80,80)",children:[s.jsx("rect",{x:"-20",y:"-15",width:"40",height:"25",rx:"3",fill:"#f8fafc",stroke:"#e2e8f0",strokeWidth:"2"}),s.jsx("circle",{cx:"0",cy:"-2",r:"3",fill:"#22c55e"}),s.jsx("rect",{x:"-8",y:"5",width:"16",height:"2",fill:"#e2e8f0"}),s.jsx("rect",{x:"-6",y:"8",width:"12",height:"1",fill:"#e2e8f0"})]}),s.jsxs("g",{transform:"translate(320,80)",children:[s.jsx("rect",{x:"-20",y:"-15",width:"40",height:"25",rx:"3",fill:"#f8fafc",stroke:"#e2e8f0",strokeWidth:"2"}),s.jsx("circle",{cx:"0",cy:"-2",r:"3",fill:"#3b82f6"}),s.jsx("rect",{x:"-8",y:"5",width:"16",height:"2",fill:"#e2e8f0"}),s.jsx("rect",{x:"-6",y:"8",width:"12",height:"1",fill:"#e2e8f0"})]}),s.jsx("path",{d:"M100 85 Q150 60 180 120",stroke:"#22c55e",strokeWidth:"2",fill:"none",strokeDasharray:"5,5",opacity:"0.6"}),s.jsx("path",{d:"M300 85 Q250 60 220 120",stroke:"#3b82f6",strokeWidth:"2",fill:"none",strokeDasharray:"5,5",opacity:"0.6"}),s.jsx("circle",{cx:"60",cy:"120",r:"4",fill:"#22c55e",opacity:"0.7",children:s.jsx("animate",{attributeName:"cy",values:"115;125;115",dur:"3s",repeatCount:"indefinite"})}),s.jsx("circle",{cx:"340",cy:"140",r:"3",fill:"#3b82f6",opacity:"0.7",children:s.jsx("animate",{attributeName:"cy",values:"135;145;135",dur:"2.5s",repeatCount:"indefinite"})}),s.jsx("circle",{cx:"80",cy:"180",r:"2",fill:"#16a34a",opacity:"0.5",children:s.jsx("animate",{attributeName:"cy",values:"175;185;175",dur:"4s",repeatCount:"indefinite"})}),s.jsxs("g",{transform:"translate(200,60)",children:[s.jsx("circle",{cx:"0",cy:"0",r:"12",fill:"#fbbf24",opacity:"0.2"}),s.jsx("text",{x:"0",y:"4",textAnchor:"middle",fontSize:"12",fill:"#f59e0b",fontWeight:"bold",children:"₹"}),s.jsx("animate",{attributeName:"opacity",values:"0.5;1;0.5",dur:"2s",repeatCount:"indefinite"})]})]})})})]})})}),s.jsx("section",{id:"contact",className:"py-20 px-4",children:s.jsxs("div",{className:"container mx-auto",children:[s.jsxs("div",{className:"text-center mb-16",children:[s.jsx(f,{className:"mb-4 bg-bank-blue-100 text-bank-blue-800",children:"Hubungi Kami"}),s.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-800 mb-4",children:"Ada Pertanyaan? Kami Siap Membantu"}),s.jsx("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Tim customer service kami siap membantu Anda 24/7. Jangan ragu untuk menghubungi kami."})]}),s.jsxs("div",{className:"grid md:grid-cols-3 gap-8",children:[s.jsxs(h,{className:"text-center hover-scale border-0 shadow-lg",children:[s.jsxs(g,{children:[s.jsx("div",{className:"w-16 h-16 mx-auto mb-4 bg-bank-green-100 rounded-2xl flex items-center justify-center",children:s.jsx(x,{className:"w-8 h-8 text-bank-green-600"})}),s.jsx(u,{children:"Telepon"})]}),s.jsxs(j,{children:[s.jsx("p",{className:"text-gray-600 mb-2",children:"Hubungi kami langsung"}),s.jsx("p",{className:"font-semibold text-bank-green-600",children:"+62 812 3456 7890"})]})]}),s.jsxs(h,{className:"text-center hover-scale border-0 shadow-lg",children:[s.jsxs(g,{children:[s.jsx("div",{className:"w-16 h-16 mx-auto mb-4 bg-bank-blue-100 rounded-2xl flex items-center justify-center",children:s.jsx(m,{className:"w-8 h-8 text-bank-blue-600"})}),s.jsx(u,{children:"Email"})]}),s.jsxs(j,{children:[s.jsx("p",{className:"text-gray-600 mb-2",children:"Kirim email ke kami"}),s.jsx("p",{className:"font-semibold text-bank-blue-600",children:"<EMAIL>"})]})]}),s.jsxs(h,{className:"text-center hover-scale border-0 shadow-lg",children:[s.jsxs(g,{children:[s.jsx("div",{className:"w-16 h-16 mx-auto mb-4 bg-purple-100 rounded-2xl flex items-center justify-center",children:s.jsx(d,{className:"w-8 h-8 text-purple-600"})}),s.jsx(u,{children:"Lokasi"})]}),s.jsxs(j,{children:[s.jsx("p",{className:"text-gray-600 mb-2",children:"Kunjungi kantor kami"}),s.jsx("p",{className:"font-semibold text-purple-600",children:"Jakarta, Indonesia"})]})]})]})]})}),s.jsx("footer",{className:"bg-gray-800 text-white py-12 px-4",children:s.jsxs("div",{className:"container mx-auto",children:[s.jsxs("div",{className:"grid md:grid-cols-4 gap-8",children:[s.jsxs("div",{children:[s.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[s.jsx("div",{className:"w-10 h-10 bg-gradient-green rounded-xl flex items-center justify-center",children:s.jsx(n,{className:"w-6 h-6 text-white"})}),s.jsx("span",{className:"text-xl font-bold",children:"Bank Sampah Digital"})]}),s.jsx("p",{className:"text-gray-400",children:"Platform digital untuk mengelola sampah dengan cara yang mudah, efisien, dan menguntungkan."})]}),s.jsxs("div",{children:[s.jsx("h3",{className:"font-semibold mb-4",children:"Menu"}),s.jsxs("div",{className:"space-y-2 text-gray-400",children:[s.jsx("a",{href:"#home",className:"block hover:text-white transition-colors",children:"Beranda"}),s.jsx("a",{href:"#features",className:"block hover:text-white transition-colors",children:"Fitur"}),s.jsx("a",{href:"#about",className:"block hover:text-white transition-colors",children:"Tentang"}),s.jsx("a",{href:"#contact",className:"block hover:text-white transition-colors",children:"Kontak"})]})]}),s.jsxs("div",{children:[s.jsx("h3",{className:"font-semibold mb-4",children:"Layanan"}),s.jsxs("div",{className:"space-y-2 text-gray-400",children:[s.jsx("p",{children:"Tukar Sampah"}),s.jsx("p",{children:"Jemput Sampah"}),s.jsx("p",{children:"Tracking Poin"}),s.jsx("p",{children:"Customer Service"})]})]}),s.jsxs("div",{children:[s.jsx("h3",{className:"font-semibold mb-4",children:"Kontak"}),s.jsxs("div",{className:"space-y-2 text-gray-400",children:[s.jsx("p",{children:"+62 812 3456 7890"}),s.jsx("p",{children:"<EMAIL>"}),s.jsx("p",{children:"Jakarta, Indonesia"})]})]})]}),s.jsx("div",{className:"border-t border-gray-700 mt-8 pt-8 text-center text-gray-400",children:s.jsx("p",{children:"© 2024 Bank Sampah Digital. All rights reserved."})})]})})]})};export{k as default};
