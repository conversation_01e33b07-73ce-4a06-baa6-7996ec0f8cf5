import{r as e,j as r}from"./react-vendor-BH2w6qpU.js";import{c as o}from"./index-BSlVarex.js";const s=e.forwardRef((({className:e,type:s,...t},i)=>r.jsx("input",{type:s,className:o("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:i,...t})));s.displayName="Input";export{s as I};
