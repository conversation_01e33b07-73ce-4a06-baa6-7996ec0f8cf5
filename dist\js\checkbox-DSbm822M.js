import{r as e,j as s,q as r,s as a,t as i}from"./react-vendor-BH2w6qpU.js";import{c as t}from"./index-BSlVarex.js";const o=e.forwardRef((({className:e,...o},d)=>s.jsx(r,{ref:d,className:t("peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...o,children:s.jsx(a,{className:t("flex items-center justify-center text-current"),children:s.jsx(i,{className:"h-4 w-4"})})})));o.displayName=r.displayName;export{o as C};
