import{f as e,r as a,j as s,U as r,g as i,S as t,h as l,w as n,p as c,x as o,y as d,z as m}from"./react-vendor-BH2w6qpU.js";import{C as x,c as g,a as h,b as p,d as u}from"./card-DfBXoe59.js";import{B as j}from"./badge-DqYU9d-0.js";import{D as b}from"./vendor-BGyKCyRD.js";import{A as N}from"./AdminSidebar-B0U-Ar4Y.js";import{S as v}from"./index-BSlVarex.js";import"./radix-vendor-CttiZxwU.js";import"./utils-vendor-DeZn2tlB.js";import"./query-vendor-B0Wv6VB8.js";const y=()=>{const y=e(),[f,k]=a.useState(null),[w,A]=a.useState(!0);a.useEffect((()=>{(async()=>{A(!0),await new Promise((e=>setTimeout(e,1500)));const e=localStorage.getItem("user");if(!e)return void y("/login");const a=JSON.parse(e);if("admin"!==a.role)return b.error("Akses Ditolak",{description:"Anda tidak memiliki akses ke halaman admin"}),void y("/");k(a),A(!1)})()}),[y]);const T=[{title:"Total Nasabah",value:"2,847",change:"+12%",changeType:"increase",icon:r,color:"text-blue-600",bgColor:"bg-blue-100"},{title:"Sampah Terkumpul",value:"1,245 Kg",change:"+8%",changeType:"increase",icon:i,color:"text-green-600",bgColor:"bg-green-100"},{title:"Total Transaksi",value:"5,692",change:"+23%",changeType:"increase",icon:t,color:"text-purple-600",bgColor:"bg-purple-100"},{title:"Poin Dibagikan",value:"78,540",change:"+15%",changeType:"increase",icon:l,color:"text-orange-600",bgColor:"bg-orange-100"}],K=[{title:"Kelola Nasabah",description:"Tambah, edit, atau hapus data nasabah",icon:c,color:"bg-blue-500 hover:bg-blue-600",action:()=>b.info("Fitur Kelola Nasabah",{description:"Akan segera tersedia"})},{title:"Kelola Kategori",description:"Atur kategori dan jenis sampah",icon:o,color:"bg-green-500 hover:bg-green-600",action:()=>b.info("Fitur Kelola Kategori",{description:"Akan segera tersedia"})},{title:"Kelola Transaksi",description:"Monitor dan kelola transaksi",icon:t,color:"bg-purple-500 hover:bg-purple-600",action:()=>b.info("Fitur Kelola Transaksi",{description:"Akan segera tersedia"})},{title:"Kelola Jemput Sampah",description:"Atur permintaan jemput sampah",icon:d,color:"bg-orange-500 hover:bg-orange-600",action:()=>b.info("Fitur Kelola Jemput",{description:"Akan segera tersedia"})}];return w?s.jsxs("div",{className:"min-h-screen bg-gray-50 flex",children:[s.jsx("div",{className:"w-64 bg-white shadow-xl border-r border-gray-200",children:s.jsxs("div",{className:"p-4 space-y-4",children:[s.jsx("div",{className:"h-12 bg-gray-200 rounded animate-pulse"}),s.jsx("div",{className:"space-y-2",children:[...Array(6)].map(((e,a)=>s.jsx("div",{className:"h-10 bg-gray-200 rounded animate-pulse"},a)))})]})}),s.jsx("div",{className:"flex-1 p-8",children:s.jsx(v,{type:"dashboard"})})]}):f?s.jsxs("div",{className:"min-h-screen bg-gray-50 flex",children:[s.jsx(N,{}),s.jsx("div",{className:"flex-1 lg:ml-0",children:s.jsxs("main",{className:"p-4 lg:p-8",children:[s.jsxs("div",{className:"mb-8 animate-fade-in",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Dashboard Admin"}),s.jsx("p",{className:"text-gray-600",children:"Berikut adalah ringkasan aktivitas Bank Sampah Digital hari ini"})]}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:T.map(((e,a)=>s.jsx(x,{className:"hover:shadow-lg transition-shadow duration-300 hover-scale",children:s.jsx(g,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:e.title}),s.jsx("p",{className:"text-2xl font-bold text-gray-800",children:e.value}),s.jsxs("div",{className:"flex items-center mt-2",children:[s.jsx(j,{variant:"secondary",className:"text-xs "+("increase"===e.changeType?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.change}),s.jsx("span",{className:"text-xs text-gray-500 ml-2",children:"dari bulan lalu"})]})]}),s.jsx("div",{className:`w-12 h-12 ${e.bgColor} rounded-xl flex items-center justify-center`,children:s.jsx(e.icon,{className:`w-6 h-6 ${e.color}`})})]})})},a)))}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[s.jsx("div",{className:"lg:col-span-2",children:s.jsxs(x,{className:"h-fit",children:[s.jsxs(h,{children:[s.jsxs(p,{className:"flex items-center",children:[s.jsx(n,{className:"w-5 h-5 mr-2 text-bank-green-600"}),"Aksi Cepat"]}),s.jsx(u,{children:"Fitur-fitur utama untuk mengelola sistem"})]}),s.jsx(g,{children:s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:K.map(((e,a)=>s.jsx("div",{onClick:e.action,className:"p-4 border border-gray-200 rounded-lg hover:shadow-md transition-all duration-200 cursor-pointer hover:border-bank-green-300 group hover-scale",children:s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx("div",{className:`w-10 h-10 ${e.color} rounded-lg flex items-center justify-center transition-colors`,children:s.jsx(e.icon,{className:"w-5 h-5 text-white"})}),s.jsxs("div",{className:"flex-1",children:[s.jsx("h3",{className:"font-semibold text-gray-800 group-hover:text-bank-green-700 transition-colors",children:e.title}),s.jsx("p",{className:"text-sm text-gray-600",children:e.description})]})]})},a)))})})]})}),s.jsx("div",{children:s.jsxs(x,{children:[s.jsxs(h,{children:[s.jsxs(p,{className:"flex items-center",children:[s.jsx(m,{className:"w-5 h-5 mr-2 text-bank-blue-600"}),"Aktivitas Terbaru"]}),s.jsx(u,{children:"Aktivitas sistem dalam 24 jam terakhir"})]}),s.jsx(g,{children:s.jsx("div",{className:"space-y-4",children:[{id:1,action:"Nasabah baru mendaftar",user:"Ahmad Wijaya",time:"2 menit yang lalu",type:"user"},{id:2,action:"Transaksi sampah plastik",user:"Siti Nurhaliza",time:"15 menit yang lalu",type:"transaction"},{id:3,action:"Request jemput sampah",user:"Budi Santoso",time:"30 menit yang lalu",type:"pickup"},{id:4,action:"Kategori sampah baru ditambahkan",user:"Admin",time:"1 jam yang lalu",type:"category"}].map((e=>s.jsxs("div",{className:"flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors",children:[s.jsxs("div",{className:"w-8 h-8 rounded-full flex items-center justify-center "+("user"===e.type?"bg-blue-100":"transaction"===e.type?"bg-green-100":"pickup"===e.type?"bg-orange-100":"bg-purple-100"),children:["user"===e.type&&s.jsx(r,{className:"w-4 h-4 text-blue-600"}),"transaction"===e.type&&s.jsx(t,{className:"w-4 h-4 text-green-600"}),"pickup"===e.type&&s.jsx(d,{className:"w-4 h-4 text-orange-600"}),"category"===e.type&&s.jsx(o,{className:"w-4 h-4 text-purple-600"})]}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsx("p",{className:"text-sm font-medium text-gray-800",children:e.action}),s.jsx("p",{className:"text-sm text-gray-500",children:e.user}),s.jsx("p",{className:"text-xs text-gray-400",children:e.time})]})]},e.id)))})})]})})]})]})})]}):null};export{y as default};
