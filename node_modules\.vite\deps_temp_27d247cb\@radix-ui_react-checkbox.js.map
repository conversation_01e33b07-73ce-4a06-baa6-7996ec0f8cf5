{"version": 3, "sources": ["../../@radix-ui/react-checkbox/src/checkbox.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { useSize } from '@radix-ui/react-use-size';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\nconst CHECKBOX_NAME = 'Checkbox';\n\ntype ScopedProps<P> = P & { __scopeCheckbox?: Scope };\nconst [createCheckboxContext, createCheckboxScope] = createContextScope(CHECKBOX_NAME);\n\ntype CheckedState = boolean | 'indeterminate';\n\ntype CheckboxContextValue<State extends CheckedState | boolean = CheckedState> = {\n  checked: State | boolean;\n  setChecked: React.Dispatch<React.SetStateAction<State | boolean>>;\n  disabled: boolean | undefined;\n  control: HTMLButtonElement | null;\n  setControl: React.Dispatch<React.SetStateAction<HTMLButtonElement | null>>;\n  name: string | undefined;\n  form: string | undefined;\n  value: string | number | readonly string[];\n  hasConsumerStoppedPropagationRef: React.RefObject<boolean>;\n  required: boolean | undefined;\n  defaultChecked: boolean | undefined;\n  isFormControl: boolean;\n  bubbleInput: HTMLInputElement | null;\n  setBubbleInput: React.Dispatch<React.SetStateAction<HTMLInputElement | null>>;\n};\n\nconst [CheckboxProviderImpl, useCheckboxContext] =\n  createCheckboxContext<CheckboxContextValue>(CHECKBOX_NAME);\n\n/* -------------------------------------------------------------------------------------------------\n * CheckboxProvider\n * -----------------------------------------------------------------------------------------------*/\n\ninterface CheckboxProviderProps<State extends CheckedState = CheckedState> {\n  checked?: State | boolean;\n  defaultChecked?: State | boolean;\n  required?: boolean;\n  onCheckedChange?(checked: State | boolean): void;\n  name?: string;\n  form?: string;\n  disabled?: boolean;\n  value?: string | number | readonly string[];\n  children?: React.ReactNode;\n}\n\nfunction CheckboxProvider<State extends CheckedState = CheckedState>(\n  props: ScopedProps<CheckboxProviderProps<State>>\n) {\n  const {\n    __scopeCheckbox,\n    checked: checkedProp,\n    children,\n    defaultChecked,\n    disabled,\n    form,\n    name,\n    onCheckedChange,\n    required,\n    value = 'on',\n    // @ts-expect-error\n    internal_do_not_use_render,\n  } = props;\n\n  const [checked, setChecked] = useControllableState({\n    prop: checkedProp,\n    defaultProp: defaultChecked ?? false,\n    onChange: onCheckedChange,\n    caller: CHECKBOX_NAME,\n  });\n  const [control, setControl] = React.useState<HTMLButtonElement | null>(null);\n  const [bubbleInput, setBubbleInput] = React.useState<HTMLInputElement | null>(null);\n  const hasConsumerStoppedPropagationRef = React.useRef(false);\n  const isFormControl = control\n    ? !!form || !!control.closest('form')\n    : // We set this to true by default so that events bubble to forms without JS (SSR)\n      true;\n\n  const context: CheckboxContextValue<State> = {\n    checked: checked,\n    disabled: disabled,\n    setChecked: setChecked,\n    control: control,\n    setControl: setControl,\n    name: name,\n    form: form,\n    value: value,\n    hasConsumerStoppedPropagationRef: hasConsumerStoppedPropagationRef,\n    required: required,\n    defaultChecked: isIndeterminate(defaultChecked) ? false : defaultChecked,\n    isFormControl: isFormControl,\n    bubbleInput,\n    setBubbleInput,\n  };\n\n  return (\n    <CheckboxProviderImpl\n      scope={__scopeCheckbox}\n      {...(context as unknown as CheckboxContextValue<CheckedState>)}\n    >\n      {isFunction(internal_do_not_use_render) ? internal_do_not_use_render(context) : children}\n    </CheckboxProviderImpl>\n  );\n}\n\n/* -------------------------------------------------------------------------------------------------\n * CheckboxTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'CheckboxTrigger';\n\ninterface CheckboxTriggerProps\n  extends Omit<\n    React.ComponentPropsWithoutRef<typeof Primitive.button>,\n    keyof CheckboxProviderProps\n  > {\n  children?: React.ReactNode;\n}\n\nconst CheckboxTrigger = React.forwardRef<HTMLButtonElement, CheckboxTriggerProps>(\n  (\n    { __scopeCheckbox, onKeyDown, onClick, ...checkboxProps }: ScopedProps<CheckboxTriggerProps>,\n    forwardedRef\n  ) => {\n    const {\n      control,\n      value,\n      disabled,\n      checked,\n      required,\n      setControl,\n      setChecked,\n      hasConsumerStoppedPropagationRef,\n      isFormControl,\n      bubbleInput,\n    } = useCheckboxContext(TRIGGER_NAME, __scopeCheckbox);\n    const composedRefs = useComposedRefs(forwardedRef, setControl);\n\n    const initialCheckedStateRef = React.useRef(checked);\n    React.useEffect(() => {\n      const form = control?.form;\n      if (form) {\n        const reset = () => setChecked(initialCheckedStateRef.current);\n        form.addEventListener('reset', reset);\n        return () => form.removeEventListener('reset', reset);\n      }\n    }, [control, setChecked]);\n\n    return (\n      <Primitive.button\n        type=\"button\"\n        role=\"checkbox\"\n        aria-checked={isIndeterminate(checked) ? 'mixed' : checked}\n        aria-required={required}\n        data-state={getState(checked)}\n        data-disabled={disabled ? '' : undefined}\n        disabled={disabled}\n        value={value}\n        {...checkboxProps}\n        ref={composedRefs}\n        onKeyDown={composeEventHandlers(onKeyDown, (event) => {\n          // According to WAI ARIA, Checkboxes don't activate on enter keypress\n          if (event.key === 'Enter') event.preventDefault();\n        })}\n        onClick={composeEventHandlers(onClick, (event) => {\n          setChecked((prevChecked) => (isIndeterminate(prevChecked) ? true : !prevChecked));\n          if (bubbleInput && isFormControl) {\n            hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n            // if checkbox has a bubble input and is a form control, stop\n            // propagation from the button so that we only propagate one click\n            // event (from the input). We propagate changes from an input so\n            // that native form validation works and form events reflect\n            // checkbox updates.\n            if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n          }\n        })}\n      />\n    );\n  }\n);\n\nCheckboxTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Checkbox\n * -----------------------------------------------------------------------------------------------*/\n\ntype CheckboxElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface CheckboxProps extends Omit<PrimitiveButtonProps, 'checked' | 'defaultChecked'> {\n  checked?: CheckedState;\n  defaultChecked?: CheckedState;\n  required?: boolean;\n  onCheckedChange?(checked: CheckedState): void;\n}\n\nconst Checkbox = React.forwardRef<CheckboxElement, CheckboxProps>(\n  (props: ScopedProps<CheckboxProps>, forwardedRef) => {\n    const {\n      __scopeCheckbox,\n      name,\n      checked,\n      defaultChecked,\n      required,\n      disabled,\n      value,\n      onCheckedChange,\n      form,\n      ...checkboxProps\n    } = props;\n\n    return (\n      <CheckboxProvider\n        __scopeCheckbox={__scopeCheckbox}\n        checked={checked}\n        defaultChecked={defaultChecked}\n        disabled={disabled}\n        required={required}\n        onCheckedChange={onCheckedChange}\n        name={name}\n        form={form}\n        value={value}\n        // @ts-expect-error\n        internal_do_not_use_render={({ isFormControl }: CheckboxContextValue) => (\n          <>\n            <CheckboxTrigger\n              {...checkboxProps}\n              ref={forwardedRef}\n              // @ts-expect-error\n              __scopeCheckbox={__scopeCheckbox}\n            />\n            {isFormControl && (\n              <CheckboxBubbleInput\n                // @ts-expect-error\n                __scopeCheckbox={__scopeCheckbox}\n              />\n            )}\n          </>\n        )}\n      />\n    );\n  }\n);\n\nCheckbox.displayName = CHECKBOX_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * CheckboxIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'CheckboxIndicator';\n\ntype CheckboxIndicatorElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface CheckboxIndicatorProps extends PrimitiveSpanProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst CheckboxIndicator = React.forwardRef<CheckboxIndicatorElement, CheckboxIndicatorProps>(\n  (props: ScopedProps<CheckboxIndicatorProps>, forwardedRef) => {\n    const { __scopeCheckbox, forceMount, ...indicatorProps } = props;\n    const context = useCheckboxContext(INDICATOR_NAME, __scopeCheckbox);\n    return (\n      <Presence\n        present={forceMount || isIndeterminate(context.checked) || context.checked === true}\n      >\n        <Primitive.span\n          data-state={getState(context.checked)}\n          data-disabled={context.disabled ? '' : undefined}\n          {...indicatorProps}\n          ref={forwardedRef}\n          style={{ pointerEvents: 'none', ...props.style }}\n        />\n      </Presence>\n    );\n  }\n);\n\nCheckboxIndicator.displayName = INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * CheckboxBubbleInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUBBLE_INPUT_NAME = 'CheckboxBubbleInput';\n\ntype InputProps = React.ComponentPropsWithoutRef<typeof Primitive.input>;\ninterface CheckboxBubbleInputProps extends Omit<InputProps, 'checked'> {}\n\nconst CheckboxBubbleInput = React.forwardRef<HTMLInputElement, CheckboxBubbleInputProps>(\n  ({ __scopeCheckbox, ...props }: ScopedProps<CheckboxBubbleInputProps>, forwardedRef) => {\n    const {\n      control,\n      hasConsumerStoppedPropagationRef,\n      checked,\n      defaultChecked,\n      required,\n      disabled,\n      name,\n      value,\n      form,\n      bubbleInput,\n      setBubbleInput,\n    } = useCheckboxContext(BUBBLE_INPUT_NAME, __scopeCheckbox);\n\n    const composedRefs = useComposedRefs(forwardedRef, setBubbleInput);\n    const prevChecked = usePrevious(checked);\n    const controlSize = useSize(control);\n\n    // Bubble checked change to parents (e.g form change event)\n    React.useEffect(() => {\n      const input = bubbleInput;\n      if (!input) return;\n\n      const inputProto = window.HTMLInputElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        inputProto,\n        'checked'\n      ) as PropertyDescriptor;\n      const setChecked = descriptor.set;\n\n      const bubbles = !hasConsumerStoppedPropagationRef.current;\n      if (prevChecked !== checked && setChecked) {\n        const event = new Event('click', { bubbles });\n        input.indeterminate = isIndeterminate(checked);\n        setChecked.call(input, isIndeterminate(checked) ? false : checked);\n        input.dispatchEvent(event);\n      }\n    }, [bubbleInput, prevChecked, checked, hasConsumerStoppedPropagationRef]);\n\n    const defaultCheckedRef = React.useRef(isIndeterminate(checked) ? false : checked);\n    return (\n      <Primitive.input\n        type=\"checkbox\"\n        aria-hidden\n        defaultChecked={defaultChecked ?? defaultCheckedRef.current}\n        required={required}\n        disabled={disabled}\n        name={name}\n        value={value}\n        form={form}\n        {...props}\n        tabIndex={-1}\n        ref={composedRefs}\n        style={{\n          ...props.style,\n          ...controlSize,\n          position: 'absolute',\n          pointerEvents: 'none',\n          opacity: 0,\n          margin: 0,\n          // We transform because the input is absolutely positioned but we have\n          // rendered it **after** the button. This pulls it back to sit on top\n          // of the button.\n          transform: 'translateX(-100%)',\n        }}\n      />\n    );\n  }\n);\n\nCheckboxBubbleInput.displayName = BUBBLE_INPUT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction isFunction(value: unknown): value is (...args: any[]) => any {\n  return typeof value === 'function';\n}\n\nfunction isIndeterminate(checked?: CheckedState): checked is 'indeterminate' {\n  return checked === 'indeterminate';\n}\n\nfunction getState(checked: CheckedState) {\n  return isIndeterminate(checked) ? 'indeterminate' : checked ? 'checked' : 'unchecked';\n}\n\nexport {\n  createCheckboxScope,\n  //\n  Checkbox,\n  CheckboxProvider,\n  CheckboxTrigger,\n  CheckboxIndicator,\n  CheckboxBubbleInput,\n  //\n  Checkbox as Root,\n  CheckboxProvider as Provider,\n  CheckboxTrigger as Trigger,\n  CheckboxIndicator as Indicator,\n  CheckboxBubbleInput as BubbleInput,\n};\nexport type {\n  CheckboxProps,\n  CheckboxProviderProps,\n  CheckboxTriggerProps,\n  CheckboxIndicatorProps,\n  CheckboxBubbleInputProps,\n  CheckedState,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAuB;AAyGnB,yBAAA;AA7FJ,IAAM,gBAAgB;AAGtB,IAAM,CAAC,uBAAuB,mBAAmB,IAAI,mBAAmB,aAAa;AAqBrF,IAAM,CAAC,sBAAsB,kBAAkB,IAC7C,sBAA4C,aAAa;AAkB3D,SAAS,iBACP,OACA;AACA,QAAM;IACJ;IACA,SAAS;IACT;IACA;IACA;IACA;IACA;IACA;IACA;IACA,QAAQ;;IAER;EACF,IAAI;AAEJ,QAAM,CAAC,SAAS,UAAU,IAAI,qBAAqB;IACjD,MAAM;IACN,aAAa,kBAAkB;IAC/B,UAAU;IACV,QAAQ;EACV,CAAC;AACD,QAAM,CAAC,SAAS,UAAU,IAAU,eAAmC,IAAI;AAC3E,QAAM,CAAC,aAAa,cAAc,IAAU,eAAkC,IAAI;AAClF,QAAM,mCAAyC,aAAO,KAAK;AAC3D,QAAM,gBAAgB,UAClB,CAAC,CAAC,QAAQ,CAAC,CAAC,QAAQ,QAAQ,MAAM;;IAElC;;AAEJ,QAAM,UAAuC;IAC3C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,gBAAgB,gBAAgB,cAAc,IAAI,QAAQ;IAC1D;IACA;IACA;EACF;AAEA,aACE;IAAC;IAAA;MACC,OAAO;MACN,GAAI;MAEJ,UAAA,WAAW,0BAA0B,IAAI,2BAA2B,OAAO,IAAI;IAAA;EAClF;AAEJ;AAMA,IAAM,eAAe;AAUrB,IAAM,kBAAwB;EAC5B,CACE,EAAE,iBAAiB,WAAW,SAAS,GAAG,cAAc,GACxD,iBACG;AACH,UAAM;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF,IAAI,mBAAmB,cAAc,eAAe;AACpD,UAAM,eAAe,gBAAgB,cAAc,UAAU;AAE7D,UAAM,yBAA+B,aAAO,OAAO;AAC7C,IAAA,gBAAU,MAAM;AACpB,YAAM,OAAO,mCAAS;AACtB,UAAI,MAAM;AACR,cAAM,QAAQ,MAAM,WAAW,uBAAuB,OAAO;AAC7D,aAAK,iBAAiB,SAAS,KAAK;AACpC,eAAO,MAAM,KAAK,oBAAoB,SAAS,KAAK;MACtD;IACF,GAAG,CAAC,SAAS,UAAU,CAAC;AAExB,eACE;MAAC,UAAU;MAAV;QACC,MAAK;QACL,MAAK;QACL,gBAAc,gBAAgB,OAAO,IAAI,UAAU;QACnD,iBAAe;QACf,cAAY,SAAS,OAAO;QAC5B,iBAAe,WAAW,KAAK;QAC/B;QACA;QACC,GAAG;QACJ,KAAK;QACL,WAAW,qBAAqB,WAAW,CAAC,UAAU;AAEpD,cAAI,MAAM,QAAQ,QAAS,OAAM,eAAe;QAClD,CAAC;QACD,SAAS,qBAAqB,SAAS,CAAC,UAAU;AAChD,qBAAW,CAAC,gBAAiB,gBAAgB,WAAW,IAAI,OAAO,CAAC,WAAY;AAChF,cAAI,eAAe,eAAe;AAChC,6CAAiC,UAAU,MAAM,qBAAqB;AAMtE,gBAAI,CAAC,iCAAiC,QAAS,OAAM,gBAAgB;UACvE;QACF,CAAC;MAAA;IACH;EAEJ;AACF;AAEA,gBAAgB,cAAc;AAe9B,IAAM,WAAiB;EACrB,CAAC,OAAmC,iBAAiB;AACnD,UAAM;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,GAAG;IACL,IAAI;AAEJ,eACE;MAAC;MAAA;QACC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,4BAA4B,CAAC,EAAE,cAAc,UAC3C,yBAAA,6BAAA,EACE,UAAA;cAAA;YAAC;YAAA;cACE,GAAG;cACJ,KAAK;cAEL;YAAA;UACF;UACC,qBACC;YAAC;YAAA;cAEC;YAAA;UACF;QAAA,EAAA,CAEJ;MAAA;IAEJ;EAEJ;AACF;AAEA,SAAS,cAAc;AAMvB,IAAM,iBAAiB;AAYvB,IAAM,oBAA0B;EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,iBAAiB,YAAY,GAAG,eAAe,IAAI;AAC3D,UAAM,UAAU,mBAAmB,gBAAgB,eAAe;AAClE,eACE;MAAC;MAAA;QACC,SAAS,cAAc,gBAAgB,QAAQ,OAAO,KAAK,QAAQ,YAAY;QAE/E,cAAA;UAAC,UAAU;UAAV;YACC,cAAY,SAAS,QAAQ,OAAO;YACpC,iBAAe,QAAQ,WAAW,KAAK;YACtC,GAAG;YACJ,KAAK;YACL,OAAO,EAAE,eAAe,QAAQ,GAAG,MAAM,MAAM;UAAA;QACjD;MAAA;IACF;EAEJ;AACF;AAEA,kBAAkB,cAAc;AAMhC,IAAM,oBAAoB;AAK1B,IAAM,sBAA4B;EAChC,CAAC,EAAE,iBAAiB,GAAG,MAAM,GAA0C,iBAAiB;AACtF,UAAM;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF,IAAI,mBAAmB,mBAAmB,eAAe;AAEzD,UAAM,eAAe,gBAAgB,cAAc,cAAc;AACjE,UAAM,cAAc,YAAY,OAAO;AACvC,UAAM,cAAc,QAAQ,OAAO;AAG7B,IAAA,gBAAU,MAAM;AACpB,YAAM,QAAQ;AACd,UAAI,CAAC,MAAO;AAEZ,YAAM,aAAa,OAAO,iBAAiB;AAC3C,YAAM,aAAa,OAAO;QACxB;QACA;MACF;AACA,YAAM,aAAa,WAAW;AAE9B,YAAM,UAAU,CAAC,iCAAiC;AAClD,UAAI,gBAAgB,WAAW,YAAY;AACzC,cAAM,QAAQ,IAAI,MAAM,SAAS,EAAE,QAAQ,CAAC;AAC5C,cAAM,gBAAgB,gBAAgB,OAAO;AAC7C,mBAAW,KAAK,OAAO,gBAAgB,OAAO,IAAI,QAAQ,OAAO;AACjE,cAAM,cAAc,KAAK;MAC3B;IACF,GAAG,CAAC,aAAa,aAAa,SAAS,gCAAgC,CAAC;AAExE,UAAM,oBAA0B,aAAO,gBAAgB,OAAO,IAAI,QAAQ,OAAO;AACjF,eACE;MAAC,UAAU;MAAV;QACC,MAAK;QACL,eAAW;QACX,gBAAgB,kBAAkB,kBAAkB;QACpD;QACA;QACA;QACA;QACA;QACC,GAAG;QACJ,UAAU;QACV,KAAK;QACL,OAAO;UACL,GAAG,MAAM;UACT,GAAG;UACH,UAAU;UACV,eAAe;UACf,SAAS;UACT,QAAQ;;;;UAIR,WAAW;QACb;MAAA;IACF;EAEJ;AACF;AAEA,oBAAoB,cAAc;AAIlC,SAAS,WAAW,OAAkD;AACpE,SAAO,OAAO,UAAU;AAC1B;AAEA,SAAS,gBAAgB,SAAoD;AAC3E,SAAO,YAAY;AACrB;AAEA,SAAS,SAAS,SAAuB;AACvC,SAAO,gBAAgB,OAAO,IAAI,kBAAkB,UAAU,YAAY;AAC5E;", "names": []}