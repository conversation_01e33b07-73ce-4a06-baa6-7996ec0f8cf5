import{r as t}from"./react-vendor-C9kdeAq4.js";var e={exports:{}},n={};
/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
!function(t){function e(t,e){var n=t.length;t.push(e);t:for(;0<n;){var r=n-1>>>1,i=t[r];if(!(0<o(i,e)))break t;t[r]=e,t[n]=i,n=r}}function n(t){return 0===t.length?null:t[0]}function r(t){if(0===t.length)return null;var e=t[0],n=t.pop();if(n!==e){t[0]=n;t:for(var r=0,i=t.length,a=i>>>1;r<a;){var l=2*(r+1)-1,s=t[l],c=l+1,u=t[c];if(0>o(s,n))c<i&&0>o(u,s)?(t[r]=u,t[c]=n,r=c):(t[r]=s,t[l]=n,r=l);else{if(!(c<i&&0>o(u,n)))break t;t[r]=u,t[c]=n,r=c}}}return e}function o(t,e){var n=t.sortIndex-e.sortIndex;return 0!==n?n:t.id-e.id}if("object"==typeof performance&&"function"==typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var a=Date,l=a.now();t.unstable_now=function(){return a.now()-l}}var s=[],c=[],u=1,f=null,h=3,d=!1,p=!1,m=!1,g="function"==typeof setTimeout?setTimeout:null,y="function"==typeof clearTimeout?clearTimeout:null,v="undefined"!=typeof setImmediate?setImmediate:null;function w(t){for(var o=n(c);null!==o;){if(null===o.callback)r(c);else{if(!(o.startTime<=t))break;r(c),o.sortIndex=o.expirationTime,e(s,o)}o=n(c)}}function b(t){if(m=!1,w(t),!p)if(null!==n(s))p=!0,C(x);else{var e=n(c);null!==e&&W(b,e.startTime-t)}}function x(e,o){p=!1,m&&(m=!1,y(P),P=-1),d=!0;var i=h;try{for(w(o),f=n(s);null!==f&&(!(f.expirationTime>o)||e&&!T());){var a=f.callback;if("function"==typeof a){f.callback=null,h=f.priorityLevel;var l=a(f.expirationTime<=o);o=t.unstable_now(),"function"==typeof l?f.callback=l:f===n(s)&&r(s),w(o)}else r(s);f=n(s)}if(null!==f)var u=!0;else{var g=n(c);null!==g&&W(b,g.startTime-o),u=!1}return u}finally{f=null,h=i,d=!1}}"undefined"!=typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var A,E=!1,R=null,P=-1,k=5,L=-1;function T(){return!(t.unstable_now()-L<k)}function S(){if(null!==R){var e=t.unstable_now();L=e;var n=!0;try{n=R(!0,e)}finally{n?A():(E=!1,R=null)}}else E=!1}if("function"==typeof v)A=function(){v(S)};else if("undefined"!=typeof MessageChannel){var O=new MessageChannel,_=O.port2;O.port1.onmessage=S,A=function(){_.postMessage(null)}}else A=function(){g(S,0)};function C(t){R=t,E||(E=!0,A())}function W(e,n){P=g((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(t){t.callback=null},t.unstable_continueExecution=function(){p||d||(p=!0,C(x))},t.unstable_forceFrameRate=function(t){0>t||125<t||(k=0<t?Math.floor(1e3/t):5)},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_getFirstCallbackNode=function(){return n(s)},t.unstable_next=function(t){switch(h){case 1:case 2:case 3:var e=3;break;default:e=h}var n=h;h=e;try{return t()}finally{h=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(t,e){switch(t){case 1:case 2:case 3:case 4:case 5:break;default:t=3}var n=h;h=t;try{return e()}finally{h=n}},t.unstable_scheduleCallback=function(r,o,i){var a=t.unstable_now();switch("object"==typeof i&&null!==i?i="number"==typeof(i=i.delay)&&0<i?a+i:a:i=a,r){case 1:var l=-1;break;case 2:l=250;break;case 5:l=1073741823;break;case 4:l=1e4;break;default:l=5e3}return r={id:u++,callback:o,priorityLevel:r,startTime:i,expirationTime:l=i+l,sortIndex:-1},i>a?(r.sortIndex=i,e(c,r),null===n(s)&&r===n(c)&&(m?(y(P),P=-1):m=!0,W(b,i-a))):(r.sortIndex=l,e(s,r),p||d||(p=!0,C(x))),r},t.unstable_shouldYield=T,t.unstable_wrapCallback=function(t){var e=h;return function(){var n=h;h=e;try{return t.apply(this,arguments)}finally{h=n}}}}(n),e.exports=n;var r=e.exports;const o=["top","right","bottom","left"],i=Math.min,a=Math.max,l=Math.round,s=Math.floor,c=t=>({x:t,y:t}),u={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function h(t,e,n){return a(t,i(e,n))}function d(t,e){return"function"==typeof t?t(e):t}function p(t){return t.split("-")[0]}function m(t){return t.split("-")[1]}function g(t){return"x"===t?"y":"x"}function y(t){return"y"===t?"height":"width"}function v(t){return["top","bottom"].includes(p(t))?"y":"x"}function w(t){return g(v(t))}function b(t){return t.replace(/start|end/g,(t=>f[t]))}function x(t){return t.replace(/left|right|bottom|top/g,(t=>u[t]))}function A(t){return"number"!=typeof t?function(t){return{top:0,right:0,bottom:0,left:0,...t}}(t):{top:t,right:t,bottom:t,left:t}}function E(t){const{x:e,y:n,width:r,height:o}=t;return{width:r,height:o,top:n,left:e,right:e+r,bottom:n+o,x:e,y:n}}function R(t,e,n){let{reference:r,floating:o}=t;const i=v(e),a=w(e),l=y(a),s=p(e),c="y"===i,u=r.x+r.width/2-o.width/2,f=r.y+r.height/2-o.height/2,h=r[l]/2-o[l]/2;let d;switch(s){case"top":d={x:u,y:r.y-o.height};break;case"bottom":d={x:u,y:r.y+r.height};break;case"right":d={x:r.x+r.width,y:f};break;case"left":d={x:r.x-o.width,y:f};break;default:d={x:r.x,y:r.y}}switch(m(e)){case"start":d[a]-=h*(n&&c?-1:1);break;case"end":d[a]+=h*(n&&c?-1:1)}return d}async function P(t,e){var n;void 0===e&&(e={});const{x:r,y:o,platform:i,rects:a,elements:l,strategy:s}=t,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:f="floating",altBoundary:h=!1,padding:p=0}=d(e,t),m=A(p),g=l[h?"floating"===f?"reference":"floating":f],y=E(await i.getClippingRect({element:null==(n=await(null==i.isElement?void 0:i.isElement(g)))||n?g:g.contextElement||await(null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:u,strategy:s})),v="floating"===f?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,w=await(null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),b=await(null==i.isElement?void 0:i.isElement(w))&&await(null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},x=E(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:v,offsetParent:w,strategy:s}):v);return{top:(y.top-x.top+m.top)/b.y,bottom:(x.bottom-y.bottom+m.bottom)/b.y,left:(y.left-x.left+m.left)/b.x,right:(x.right-y.right+m.right)/b.x}}function k(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function L(t){return o.some((e=>t[e]>=0))}function T(){return"undefined"!=typeof window}function S(t){return C(t)?(t.nodeName||"").toLowerCase():"#document"}function O(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function _(t){var e;return null==(e=(C(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function C(t){return!!T()&&(t instanceof Node||t instanceof O(t).Node)}function W(t){return!!T()&&(t instanceof Element||t instanceof O(t).Element)}function M(t){return!!T()&&(t instanceof HTMLElement||t instanceof O(t).HTMLElement)}function D(t){return!(!T()||"undefined"==typeof ShadowRoot)&&(t instanceof ShadowRoot||t instanceof O(t).ShadowRoot)}function j(t){const{overflow:e,overflowX:n,overflowY:r,display:o}=N(t);return/auto|scroll|overlay|hidden|clip/.test(e+r+n)&&!["inline","contents"].includes(o)}function B(t){return["table","td","th"].includes(S(t))}function I(t){return[":popover-open",":modal"].some((e=>{try{return t.matches(e)}catch(n){return!1}}))}function H(t){const e=$(),n=W(t)?N(t):t;return["transform","translate","scale","rotate","perspective"].some((t=>!!n[t]&&"none"!==n[t]))||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some((t=>(n.willChange||"").includes(t)))||["paint","layout","strict","content"].some((t=>(n.contain||"").includes(t)))}function $(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function F(t){return["html","body","#document"].includes(S(t))}function N(t){return O(t).getComputedStyle(t)}function V(t){return W(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function z(t){if("html"===S(t))return t;const e=t.assignedSlot||t.parentNode||D(t)&&t.host||_(t);return D(e)?e.host:e}function U(t){const e=z(t);return F(e)?t.ownerDocument?t.ownerDocument.body:t.body:M(e)&&j(e)?e:U(e)}function q(t,e,n){var r;void 0===e&&(e=[]),void 0===n&&(n=!0);const o=U(t),i=o===(null==(r=t.ownerDocument)?void 0:r.body),a=O(o);if(i){const t=Y(a);return e.concat(a,a.visualViewport||[],j(o)?o:[],t&&n?q(t):[])}return e.concat(o,q(o,[],n))}function Y(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function X(t){const e=N(t);let n=parseFloat(e.width)||0,r=parseFloat(e.height)||0;const o=M(t),i=o?t.offsetWidth:n,a=o?t.offsetHeight:r,s=l(n)!==i||l(r)!==a;return s&&(n=i,r=a),{width:n,height:r,$:s}}function J(t){return W(t)?t:t.contextElement}function G(t){const e=J(t);if(!M(e))return c(1);const n=e.getBoundingClientRect(),{width:r,height:o,$:i}=X(e);let a=(i?l(n.width):n.width)/r,s=(i?l(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),s&&Number.isFinite(s)||(s=1),{x:a,y:s}}const K=c(0);function Q(t){const e=O(t);return $()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:K}function Z(t,e,n,r){void 0===e&&(e=!1),void 0===n&&(n=!1);const o=t.getBoundingClientRect(),i=J(t);let a=c(1);e&&(r?W(r)&&(a=G(r)):a=G(t));const l=function(t,e,n){return void 0===e&&(e=!1),!(!n||e&&n!==O(t))&&e}(i,n,r)?Q(i):c(0);let s=(o.left+l.x)/a.x,u=(o.top+l.y)/a.y,f=o.width/a.x,h=o.height/a.y;if(i){const t=O(i),e=r&&W(r)?O(r):r;let n=t,o=Y(n);for(;o&&r&&e!==n;){const t=G(o),e=o.getBoundingClientRect(),r=N(o),i=e.left+(o.clientLeft+parseFloat(r.paddingLeft))*t.x,a=e.top+(o.clientTop+parseFloat(r.paddingTop))*t.y;s*=t.x,u*=t.y,f*=t.x,h*=t.y,s+=i,u+=a,n=O(o),o=Y(n)}}return E({width:f,height:h,x:s,y:u})}function tt(t,e){const n=V(t).scrollLeft;return e?e.left+n:Z(_(t)).left+n}function et(t,e,n){void 0===n&&(n=!1);const r=t.getBoundingClientRect();return{x:r.left+e.scrollLeft-(n?0:tt(t,r)),y:r.top+e.scrollTop}}function nt(t,e,n){let r;if("viewport"===e)r=function(t,e){const n=O(t),r=_(t),o=n.visualViewport;let i=r.clientWidth,a=r.clientHeight,l=0,s=0;if(o){i=o.width,a=o.height;const t=$();(!t||t&&"fixed"===e)&&(l=o.offsetLeft,s=o.offsetTop)}return{width:i,height:a,x:l,y:s}}(t,n);else if("document"===e)r=function(t){const e=_(t),n=V(t),r=t.ownerDocument.body,o=a(e.scrollWidth,e.clientWidth,r.scrollWidth,r.clientWidth),i=a(e.scrollHeight,e.clientHeight,r.scrollHeight,r.clientHeight);let l=-n.scrollLeft+tt(t);const s=-n.scrollTop;return"rtl"===N(r).direction&&(l+=a(e.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:s}}(_(t));else if(W(e))r=function(t,e){const n=Z(t,!0,"fixed"===e),r=n.top+t.clientTop,o=n.left+t.clientLeft,i=M(t)?G(t):c(1);return{width:t.clientWidth*i.x,height:t.clientHeight*i.y,x:o*i.x,y:r*i.y}}(e,n);else{const n=Q(t);r={x:e.x-n.x,y:e.y-n.y,width:e.width,height:e.height}}return E(r)}function rt(t,e){const n=z(t);return!(n===e||!W(n)||F(n))&&("fixed"===N(n).position||rt(n,e))}function ot(t,e,n){const r=M(e),o=_(e),i="fixed"===n,a=Z(t,!0,i,e);let l={scrollLeft:0,scrollTop:0};const s=c(0);function u(){s.x=tt(o)}if(r||!r&&!i)if(("body"!==S(e)||j(o))&&(l=V(e)),r){const t=Z(e,!0,i,e);s.x=t.x+e.clientLeft,s.y=t.y+e.clientTop}else o&&u();i&&!r&&o&&u();const f=!o||r||i?c(0):et(o,l);return{x:a.left+l.scrollLeft-s.x-f.x,y:a.top+l.scrollTop-s.y-f.y,width:a.width,height:a.height}}function it(t){return"static"===N(t).position}function at(t,e){if(!M(t)||"fixed"===N(t).position)return null;if(e)return e(t);let n=t.offsetParent;return _(t)===n&&(n=n.ownerDocument.body),n}function lt(t,e){const n=O(t);if(I(t))return n;if(!M(t)){let e=z(t);for(;e&&!F(e);){if(W(e)&&!it(e))return e;e=z(e)}return n}let r=at(t,e);for(;r&&B(r)&&it(r);)r=at(r,e);return r&&F(r)&&it(r)&&!H(r)?n:r||function(t){let e=z(t);for(;M(e)&&!F(e);){if(H(e))return e;if(I(e))return null;e=z(e)}return null}(t)||n}const st={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:r,strategy:o}=t;const i="fixed"===o,a=_(r),l=!!e&&I(e.floating);if(r===a||l&&i)return n;let s={scrollLeft:0,scrollTop:0},u=c(1);const f=c(0),h=M(r);if((h||!h&&!i)&&(("body"!==S(r)||j(a))&&(s=V(r)),M(r))){const t=Z(r);u=G(r),f.x=t.x+r.clientLeft,f.y=t.y+r.clientTop}const d=!a||h||i?c(0):et(a,s,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-s.scrollLeft*u.x+f.x+d.x,y:n.y*u.y-s.scrollTop*u.y+f.y+d.y}},getDocumentElement:_,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:r,strategy:o}=t;const l=[..."clippingAncestors"===n?I(e)?[]:function(t,e){const n=e.get(t);if(n)return n;let r=q(t,[],!1).filter((t=>W(t)&&"body"!==S(t))),o=null;const i="fixed"===N(t).position;let a=i?z(t):t;for(;W(a)&&!F(a);){const e=N(a),n=H(a);n||"fixed"!==e.position||(o=null),(i?!n&&!o:!n&&"static"===e.position&&o&&["absolute","fixed"].includes(o.position)||j(a)&&!n&&rt(t,a))?r=r.filter((t=>t!==a)):o=e,a=z(a)}return e.set(t,r),r}(e,this._c):[].concat(n),r],s=l[0],c=l.reduce(((t,n)=>{const r=nt(e,n,o);return t.top=a(r.top,t.top),t.right=i(r.right,t.right),t.bottom=i(r.bottom,t.bottom),t.left=a(r.left,t.left),t}),nt(e,s,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:lt,getElementRects:async function(t){const e=this.getOffsetParent||lt,n=this.getDimensions,r=await n(t.floating);return{reference:ot(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){const{width:e,height:n}=X(t);return{width:e,height:n}},getScale:G,isElement:W,isRTL:function(t){return"rtl"===N(t).direction}};function ct(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function ut(t,e,n,r){void 0===r&&(r={});const{ancestorScroll:o=!0,ancestorResize:l=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,h=J(t),d=o||l?[...h?q(h):[],...q(e)]:[];d.forEach((t=>{o&&t.addEventListener("scroll",n,{passive:!0}),l&&t.addEventListener("resize",n)}));const p=h&&u?function(t,e){let n,r=null;const o=_(t);function l(){var t;clearTimeout(n),null==(t=r)||t.disconnect(),r=null}return function c(u,f){void 0===u&&(u=!1),void 0===f&&(f=1),l();const h=t.getBoundingClientRect(),{left:d,top:p,width:m,height:g}=h;if(u||e(),!m||!g)return;const y={rootMargin:-s(p)+"px "+-s(o.clientWidth-(d+m))+"px "+-s(o.clientHeight-(p+g))+"px "+-s(d)+"px",threshold:a(0,i(1,f))||1};let v=!0;function w(e){const r=e[0].intersectionRatio;if(r!==f){if(!v)return c();r?c(!1,r):n=setTimeout((()=>{c(!1,1e-7)}),1e3)}1!==r||ct(h,t.getBoundingClientRect())||c(),v=!1}try{r=new IntersectionObserver(w,{...y,root:o.ownerDocument})}catch(b){r=new IntersectionObserver(w,y)}r.observe(t)}(!0),l}(h,n):null;let m,g=-1,y=null;c&&(y=new ResizeObserver((t=>{let[r]=t;r&&r.target===h&&y&&(y.unobserve(e),cancelAnimationFrame(g),g=requestAnimationFrame((()=>{var t;null==(t=y)||t.observe(e)}))),n()})),h&&!f&&y.observe(h),y.observe(e));let v=f?Z(t):null;return f&&function e(){const r=Z(t);v&&!ct(v,r)&&n();v=r,m=requestAnimationFrame(e)}(),n(),()=>{var t;d.forEach((t=>{o&&t.removeEventListener("scroll",n),l&&t.removeEventListener("resize",n)})),null==p||p(),null==(t=y)||t.disconnect(),y=null,f&&cancelAnimationFrame(m)}}const ft=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,r;const{x:o,y:i,placement:a,middlewareData:l}=e,s=await async function(t,e){const{placement:n,platform:r,elements:o}=t,i=await(null==r.isRTL?void 0:r.isRTL(o.floating)),a=p(n),l=m(n),s="y"===v(n),c=["left","top"].includes(a)?-1:1,u=i&&s?-1:1,f=d(e,t);let{mainAxis:h,crossAxis:g,alignmentAxis:y}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return l&&"number"==typeof y&&(g="end"===l?-1*y:y),s?{x:g*u,y:h*c}:{x:h*c,y:g*u}}(e,t);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:a}}}}},ht=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:r,placement:o}=e,{mainAxis:i=!0,crossAxis:a=!1,limiter:l={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...s}=d(t,e),c={x:n,y:r},u=await P(e,s),f=v(p(o)),m=g(f);let y=c[m],w=c[f];if(i){const t="y"===m?"bottom":"right";y=h(y+u["y"===m?"top":"left"],y,y-u[t])}if(a){const t="y"===f?"bottom":"right";w=h(w+u["y"===f?"top":"left"],w,w-u[t])}const b=l.fn({...e,[m]:y,[f]:w});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[m]:i,[f]:a}}}}}},dt=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,r;const{placement:o,middlewareData:i,rects:a,initialPlacement:l,platform:s,elements:c}=e,{mainAxis:u=!0,crossAxis:f=!0,fallbackPlacements:h,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:A="none",flipAlignment:E=!0,...R}=d(t,e);if(null!=(n=i.arrow)&&n.alignmentOffset)return{};const k=p(o),L=v(l),T=p(l)===l,S=await(null==s.isRTL?void 0:s.isRTL(c.floating)),O=h||(T||!E?[x(l)]:function(t){const e=x(t);return[b(t),e,b(e)]}(l)),_="none"!==A;!h&&_&&O.push(...function(t,e,n,r){const o=m(t);let i=function(t,e,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],a=["bottom","top"];switch(t){case"top":case"bottom":return n?e?o:r:e?r:o;case"left":case"right":return e?i:a;default:return[]}}(p(t),"start"===n,r);return o&&(i=i.map((t=>t+"-"+o)),e&&(i=i.concat(i.map(b)))),i}(l,E,A,S));const C=[l,...O],W=await P(e,R),M=[];let D=(null==(r=i.flip)?void 0:r.overflows)||[];if(u&&M.push(W[k]),f){const t=function(t,e,n){void 0===n&&(n=!1);const r=m(t),o=w(t),i=y(o);let a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return e.reference[i]>e.floating[i]&&(a=x(a)),[a,x(a)]}(o,a,S);M.push(W[t[0]],W[t[1]])}if(D=[...D,{placement:o,overflows:M}],!M.every((t=>t<=0))){var j,B;const t=((null==(j=i.flip)?void 0:j.index)||0)+1,e=C[t];if(e){if(!("alignment"===f&&L!==v(e))||D.every((t=>t.overflows[0]>0&&v(t.placement)===L)))return{data:{index:t,overflows:D},reset:{placement:e}}}let n=null==(B=D.filter((t=>t.overflows[0]<=0)).sort(((t,e)=>t.overflows[1]-e.overflows[1]))[0])?void 0:B.placement;if(!n)switch(g){case"bestFit":{var I;const t=null==(I=D.filter((t=>{if(_){const e=v(t.placement);return e===L||"y"===e}return!0})).map((t=>[t.placement,t.overflows.filter((t=>t>0)).reduce(((t,e)=>t+e),0)])).sort(((t,e)=>t[1]-e[1]))[0])?void 0:I[0];t&&(n=t);break}case"initialPlacement":n=l}if(o!==n)return{reset:{placement:n}}}return{}}}},pt=function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){var n,r;const{placement:o,rects:l,platform:s,elements:c}=e,{apply:u=()=>{},...f}=d(t,e),h=await P(e,f),g=p(o),y=m(o),w="y"===v(o),{width:b,height:x}=l.floating;let A,E;"top"===g||"bottom"===g?(A=g,E=y===(await(null==s.isRTL?void 0:s.isRTL(c.floating))?"start":"end")?"left":"right"):(E=g,A="end"===y?"top":"bottom");const R=x-h.top-h.bottom,k=b-h.left-h.right,L=i(x-h[A],R),T=i(b-h[E],k),S=!e.middlewareData.shift;let O=L,_=T;if(null!=(n=e.middlewareData.shift)&&n.enabled.x&&(_=k),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(O=R),S&&!y){const t=a(h.left,0),e=a(h.right,0),n=a(h.top,0),r=a(h.bottom,0);w?_=b-2*(0!==t||0!==e?t+e:a(h.left,h.right)):O=x-2*(0!==n||0!==r?n+r:a(h.top,h.bottom))}await u({...e,availableWidth:_,availableHeight:O});const C=await s.getDimensions(c.floating);return b!==C.width||x!==C.height?{reset:{rects:!0}}:{}}}},mt=function(t){return void 0===t&&(t={}),{name:"hide",options:t,async fn(e){const{rects:n}=e,{strategy:r="referenceHidden",...o}=d(t,e);switch(r){case"referenceHidden":{const t=k(await P(e,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:L(t)}}}case"escaped":{const t=k(await P(e,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:t,escaped:L(t)}}}default:return{}}}}},gt=t=>({name:"arrow",options:t,async fn(e){const{x:n,y:r,placement:o,rects:a,platform:l,elements:s,middlewareData:c}=e,{element:u,padding:f=0}=d(t,e)||{};if(null==u)return{};const p=A(f),g={x:n,y:r},v=w(o),b=y(v),x=await l.getDimensions(u),E="y"===v,R=E?"top":"left",P=E?"bottom":"right",k=E?"clientHeight":"clientWidth",L=a.reference[b]+a.reference[v]-g[v]-a.floating[b],T=g[v]-a.reference[v],S=await(null==l.getOffsetParent?void 0:l.getOffsetParent(u));let O=S?S[k]:0;O&&await(null==l.isElement?void 0:l.isElement(S))||(O=s.floating[k]||a.floating[b]);const _=L/2-T/2,C=O/2-x[b]/2-1,W=i(p[R],C),M=i(p[P],C),D=W,j=O-x[b]-M,B=O/2-x[b]/2+_,I=h(D,B,j),H=!c.arrow&&null!=m(o)&&B!==I&&a.reference[b]/2-(B<D?W:M)-x[b]/2<0,$=H?B<D?B-D:B-j:0;return{[v]:g[v]+$,data:{[v]:I,centerOffset:B-I-$,...H&&{alignmentOffset:$}},reset:H}}}),yt=function(t){return void 0===t&&(t={}),{options:t,fn(e){const{x:n,y:r,placement:o,rects:i,middlewareData:a}=e,{offset:l=0,mainAxis:s=!0,crossAxis:c=!0}=d(t,e),u={x:n,y:r},f=v(o),h=g(f);let m=u[h],y=u[f];const w=d(l,e),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(s){const t="y"===h?"height":"width",e=i.reference[h]-i.floating[t]+b.mainAxis,n=i.reference[h]+i.reference[t]-b.mainAxis;m<e?m=e:m>n&&(m=n)}if(c){var x,A;const t="y"===h?"width":"height",e=["top","left"].includes(p(o)),n=i.reference[f]-i.floating[t]+(e&&(null==(x=a.offset)?void 0:x[f])||0)+(e?0:b.crossAxis),r=i.reference[f]+i.reference[t]+(e?0:(null==(A=a.offset)?void 0:A[f])||0)-(e?b.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:m,[f]:y}}}},vt=(t,e,n)=>{const r=new Map,o={platform:st,...n},i={...o.platform,_c:r};return(async(t,e,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),s=await(null==a.isRTL?void 0:a.isRTL(e));let c=await a.getElementRects({reference:t,floating:e,strategy:o}),{x:u,y:f}=R(c,r,s),h=r,d={},p=0;for(let m=0;m<l.length;m++){const{name:n,fn:i}=l[m],{x:g,y:y,data:v,reset:w}=await i({x:u,y:f,initialPlacement:r,placement:h,strategy:o,middlewareData:d,rects:c,platform:a,elements:{reference:t,floating:e}});u=null!=g?g:u,f=null!=y?y:f,d={...d,[n]:{...d[n],...v}},w&&p<=50&&(p++,"object"==typeof w&&(w.placement&&(h=w.placement),w.rects&&(c=!0===w.rects?await a.getElementRects({reference:t,floating:e,strategy:o}):w.rects),({x:u,y:f}=R(c,h,s))),m=-1)}return{x:u,y:f,placement:h,strategy:o,middlewareData:d}})(t,e,{...o,platform:i})};
/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function wt(){return wt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},wt.apply(this,arguments)}var bt,xt;(xt=bt||(bt={})).Pop="POP",xt.Push="PUSH",xt.Replace="REPLACE";const At="popstate";function Et(t){return void 0===t&&(t={}),function(t,e,n,r){void 0===r&&(r={});let{window:o=document.defaultView,v5Compat:i=!1}=r,a=o.history,l=bt.Pop,s=null,c=u();null==c&&(c=0,a.replaceState(wt({},a.state,{idx:c}),""));function u(){return(a.state||{idx:null}).idx}function f(){l=bt.Pop;let t=u(),e=null==t?null:t-c;c=t,s&&s({action:l,location:m.location,delta:e})}function h(t,e){l=bt.Push;let n=Lt(m.location,t,e);c=u()+1;let r=kt(n,c),f=m.createHref(n);try{a.pushState(r,"",f)}catch(h){if(h instanceof DOMException&&"DataCloneError"===h.name)throw h;o.location.assign(f)}i&&s&&s({action:l,location:m.location,delta:1})}function d(t,e){l=bt.Replace;let n=Lt(m.location,t,e);c=u();let r=kt(n,c),o=m.createHref(n);a.replaceState(r,"",o),i&&s&&s({action:l,location:m.location,delta:0})}function p(t){let e="null"!==o.location.origin?o.location.origin:o.location.href,n="string"==typeof t?t:Tt(t);return n=n.replace(/ $/,"%20"),Rt(e,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,e)}let m={get action(){return l},get location(){return t(o,a)},listen(t){if(s)throw new Error("A history only accepts one active listener");return o.addEventListener(At,f),s=t,()=>{o.removeEventListener(At,f),s=null}},createHref:t=>e(o,t),createURL:p,encodeLocation(t){let e=p(t);return{pathname:e.pathname,search:e.search,hash:e.hash}},push:h,replace:d,go:t=>a.go(t)};return m}((function(t,e){let{pathname:n,search:r,hash:o}=t.location;return Lt("",{pathname:n,search:r,hash:o},e.state&&e.state.usr||null,e.state&&e.state.key||"default")}),(function(t,e){return"string"==typeof e?e:Tt(e)}),0,t)}function Rt(t,e){if(!1===t||null==t)throw new Error(e)}function Pt(t,e){if(!t)try{throw new Error(e)}catch(n){}}function kt(t,e){return{usr:t.state,key:t.key,idx:e}}function Lt(t,e,n,r){return void 0===n&&(n=null),wt({pathname:"string"==typeof t?t:t.pathname,search:"",hash:""},"string"==typeof e?St(e):e,{state:n,key:e&&e.key||r||Math.random().toString(36).substr(2,8)})}function Tt(t){let{pathname:e="/",search:n="",hash:r=""}=t;return n&&"?"!==n&&(e+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(e+="#"===r.charAt(0)?r:"#"+r),e}function St(t){let e={};if(t){let n=t.indexOf("#");n>=0&&(e.hash=t.substr(n),t=t.substr(0,n));let r=t.indexOf("?");r>=0&&(e.search=t.substr(r),t=t.substr(0,r)),t&&(e.pathname=t)}return e}var Ot,_t;function Ct(t,e,n){return void 0===n&&(n="/"),function(t,e,n){let r="string"==typeof e?St(e):e,o=qt(r.pathname||"/",n);if(null==o)return null;let i=Wt(t);!function(t){t.sort(((t,e)=>t.score!==e.score?e.score-t.score:function(t,e){let n=t.length===e.length&&t.slice(0,-1).every(((t,n)=>t===e[n]));return n?t[t.length-1]-e[e.length-1]:0}(t.routesMeta.map((t=>t.childrenIndex)),e.routesMeta.map((t=>t.childrenIndex)))))}(i);let a=null;for(let l=0;null==a&&l<i.length;++l){let t=Ut(o);a=Vt(i[l],t)}return a}(t,e,n)}function Wt(t,e,n,r){void 0===e&&(e=[]),void 0===n&&(n=[]),void 0===r&&(r="");let o=(t,o,i)=>{let a={relativePath:void 0===i?t.path||"":i,caseSensitive:!0===t.caseSensitive,childrenIndex:o,route:t};a.relativePath.startsWith("/")&&(Rt(a.relativePath.startsWith(r),'Absolute route path "'+a.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),a.relativePath=a.relativePath.slice(r.length));let l=Gt([r,a.relativePath]),s=n.concat(a);t.children&&t.children.length>0&&(Rt(!0!==t.index,'Index routes must not have child routes. Please remove all child routes from route path "'+l+'".'),Wt(t.children,e,s,l)),(null!=t.path||t.index)&&e.push({path:l,score:Nt(l,t.index),routesMeta:s})};return t.forEach(((t,e)=>{var n;if(""!==t.path&&null!=(n=t.path)&&n.includes("?"))for(let r of Mt(t.path))o(t,e,r);else o(t,e)})),e}function Mt(t){let e=t.split("/");if(0===e.length)return[];let[n,...r]=e,o=n.endsWith("?"),i=n.replace(/\?$/,"");if(0===r.length)return o?[i,""]:[i];let a=Mt(r.join("/")),l=[];return l.push(...a.map((t=>""===t?i:[i,t].join("/")))),o&&l.push(...a),l.map((e=>t.startsWith("/")&&""===e?"/":e))}(_t=Ot||(Ot={})).data="data",_t.deferred="deferred",_t.redirect="redirect",_t.error="error";const Dt=/^:[\w-]+$/,jt=3,Bt=2,It=1,Ht=10,$t=-2,Ft=t=>"*"===t;function Nt(t,e){let n=t.split("/"),r=n.length;return n.some(Ft)&&(r+=$t),e&&(r+=Bt),n.filter((t=>!Ft(t))).reduce(((t,e)=>t+(Dt.test(e)?jt:""===e?It:Ht)),r)}function Vt(t,e,n){let{routesMeta:r}=t,o={},i="/",a=[];for(let l=0;l<r.length;++l){let t=r[l],n=l===r.length-1,s="/"===i?e:e.slice(i.length)||"/",c=zt({path:t.relativePath,caseSensitive:t.caseSensitive,end:n},s),u=t.route;if(!c)return null;Object.assign(o,c.params),a.push({params:o,pathname:Gt([i,c.pathname]),pathnameBase:Kt(Gt([i,c.pathnameBase])),route:u}),"/"!==c.pathnameBase&&(i=Gt([i,c.pathnameBase]))}return a}function zt(t,e){"string"==typeof t&&(t={path:t,caseSensitive:!1,end:!0});let[n,r]=function(t,e,n){void 0===e&&(e=!1);void 0===n&&(n=!0);Pt("*"===t||!t.endsWith("*")||t.endsWith("/*"),'Route path "'+t+'" will be treated as if it were "'+t.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+t.replace(/\*$/,"/*")+'".');let r=[],o="^"+t.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((t,e,n)=>(r.push({paramName:e,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));t.endsWith("*")?(r.push({paramName:"*"}),o+="*"===t||"/*"===t?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":""!==t&&"/"!==t&&(o+="(?:(?=\\/|$))");let i=new RegExp(o,e?void 0:"i");return[i,r]}(t.path,t.caseSensitive,t.end),o=e.match(n);if(!o)return null;let i=o[0],a=i.replace(/(.)\/+$/,"$1"),l=o.slice(1);return{params:r.reduce(((t,e,n)=>{let{paramName:r,isOptional:o}=e;if("*"===r){let t=l[n]||"";a=i.slice(0,i.length-t.length).replace(/(.)\/+$/,"$1")}const s=l[n];return t[r]=o&&!s?void 0:(s||"").replace(/%2F/g,"/"),t}),{}),pathname:i,pathnameBase:a,pattern:t}}function Ut(t){try{return t.split("/").map((t=>decodeURIComponent(t).replace(/\//g,"%2F"))).join("/")}catch(e){return Pt(!1,'The URL path "'+t+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+e+")."),t}}function qt(t,e){if("/"===e)return t;if(!t.toLowerCase().startsWith(e.toLowerCase()))return null;let n=e.endsWith("/")?e.length-1:e.length,r=t.charAt(n);return r&&"/"!==r?null:t.slice(n)||"/"}function Yt(t,e,n,r){return"Cannot include a '"+t+"' character in a manually specified `to."+e+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function Xt(t,e){let n=function(t){return t.filter(((t,e)=>0===e||t.route.path&&t.route.path.length>0))}(t);return e?n.map(((t,e)=>e===n.length-1?t.pathname:t.pathnameBase)):n.map((t=>t.pathnameBase))}function Jt(t,e,n,r){let o;void 0===r&&(r=!1),"string"==typeof t?o=St(t):(o=wt({},t),Rt(!o.pathname||!o.pathname.includes("?"),Yt("?","pathname","search",o)),Rt(!o.pathname||!o.pathname.includes("#"),Yt("#","pathname","hash",o)),Rt(!o.search||!o.search.includes("#"),Yt("#","search","hash",o)));let i,a=""===t||""===o.pathname,l=a?"/":o.pathname;if(null==l)i=n;else{let t=e.length-1;if(!r&&l.startsWith("..")){let e=l.split("/");for(;".."===e[0];)e.shift(),t-=1;o.pathname=e.join("/")}i=t>=0?e[t]:"/"}let s=function(t,e){void 0===e&&(e="/");let{pathname:n,search:r="",hash:o=""}="string"==typeof t?St(t):t,i=n?n.startsWith("/")?n:function(t,e){let n=e.replace(/\/+$/,"").split("/");return t.split("/").forEach((t=>{".."===t?n.length>1&&n.pop():"."!==t&&n.push(t)})),n.length>1?n.join("/"):"/"}(n,e):e;return{pathname:i,search:Qt(r),hash:Zt(o)}}(o,i),c=l&&"/"!==l&&l.endsWith("/"),u=(a||"."===l)&&n.endsWith("/");return s.pathname.endsWith("/")||!c&&!u||(s.pathname+="/"),s}const Gt=t=>t.join("/").replace(/\/\/+/g,"/"),Kt=t=>t.replace(/\/+$/,"").replace(/^\/*/,"/"),Qt=t=>t&&"?"!==t?t.startsWith("?")?t:"?"+t:"",Zt=t=>t&&"#"!==t?t.startsWith("#")?t:"#"+t:"";function te(t){return null!=t&&"number"==typeof t.status&&"string"==typeof t.statusText&&"boolean"==typeof t.internal&&"data"in t}const ee=["post","put","patch","delete"];new Set(ee);const ne=["get",...ee];new Set(ne);var re=function(){return re=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},re.apply(this,arguments)};function oe(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(t);o<r.length;o++)e.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(t,r[o])&&(n[r[o]]=t[r[o]])}return n}function ie(t,e,n){if(n||2===arguments.length)for(var r,o=0,i=e.length;o<i;o++)!r&&o in e||(r||(r=Array.prototype.slice.call(e,0,o)),r[o]=e[o]);return t.concat(r||Array.prototype.slice.call(e))}function ae(t,e){return"function"==typeof t?t(e):t&&(t.current=e),t}"function"==typeof SuppressedError&&SuppressedError;var le="undefined"!=typeof window?t.useLayoutEffect:t.useEffect,se=new WeakMap;function ce(e,n){var r,o,i,a=(r=null,o=function(t){return e.forEach((function(e){return ae(e,t)}))},(i=t.useState((function(){return{value:r,callback:o,facade:{get current(){return i.value},set current(t){var e=i.value;e!==t&&(i.value=t,i.callback(t,e))}}}}))[0]).callback=o,i.facade);return le((function(){var t=se.get(a);if(t){var n=new Set(t),r=new Set(e),o=a.current;n.forEach((function(t){r.has(t)||ae(t,null)})),r.forEach((function(t){n.has(t)||ae(t,o)}))}se.set(a,e)}),[e]),a}function ue(t){return t}function fe(t){void 0===t&&(t={});var e=function(t,e){void 0===e&&(e=ue);var n=[],r=!1;return{read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:t},useMedium:function(t){var o=e(t,r);return n.push(o),function(){n=n.filter((function(t){return t!==o}))}},assignSyncMedium:function(t){for(r=!0;n.length;){var e=n;n=[],e.forEach(t)}n={push:function(e){return t(e)},filter:function(){return n}}},assignMedium:function(t){r=!0;var e=[];if(n.length){var o=n;n=[],o.forEach(t),e=n}var i=function(){var n=e;e=[],n.forEach(t)},a=function(){return Promise.resolve().then(i)};a(),n={push:function(t){e.push(t),a()},filter:function(t){return e=e.filter(t),n}}}}}(null);return e.options=re({async:!0,ssr:!1},t),e}var he=function(e){var n=e.sideCar,r=oe(e,["sideCar"]);if(!n)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var o=n.read();if(!o)throw new Error("Sidecar medium not found");return t.createElement(o,re({},r))};function de(t,e){return t.useMedium(e),he}he.isSideCarExport=!0;var pe=function(){if("undefined"!=typeof __webpack_nonce__)return __webpack_nonce__},me=new WeakMap,ge=new WeakMap,ye={},ve=0,we=function(t){return t&&(t.host||we(t.parentNode))},be=function(t,e,n,r){var o=function(t,e){return e.map((function(e){if(t.contains(e))return e;var n=we(e);return n&&t.contains(n)?n:null})).filter((function(t){return Boolean(t)}))}(e,Array.isArray(t)?t:[t]);ye[n]||(ye[n]=new WeakMap);var i=ye[n],a=[],l=new Set,s=new Set(o),c=function(t){t&&!l.has(t)&&(l.add(t),c(t.parentNode))};o.forEach(c);var u=function(t){t&&!s.has(t)&&Array.prototype.forEach.call(t.children,(function(t){if(l.has(t))u(t);else try{var e=t.getAttribute(r),o=null!==e&&"false"!==e,s=(me.get(t)||0)+1,c=(i.get(t)||0)+1;me.set(t,s),i.set(t,c),a.push(t),1===s&&o&&ge.set(t,!0),1===c&&t.setAttribute(n,"true"),o||t.setAttribute(r,"true")}catch(f){}}))};return u(e),l.clear(),ve++,function(){a.forEach((function(t){var e=me.get(t)-1,o=i.get(t)-1;me.set(t,e),i.set(t,o),e||(ge.has(t)||t.removeAttribute(r),ge.delete(t)),o||t.removeAttribute(n)})),--ve||(me=new WeakMap,me=new WeakMap,ge=new WeakMap,ye={})}},xe=function(t,e,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(t)?t:[t]),o=function(t){return"undefined"==typeof document?null:(Array.isArray(t)?t[0]:t).ownerDocument.body}(t);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),be(r,o,n,"aria-hidden")):function(){return null}};export{bt as A,de as B,xe as C,oe as _,ht as a,pt as b,vt as c,gt as d,ut as e,dt as f,Xt as g,mt as h,Rt as i,Gt as j,qt as k,yt as l,Ct as m,te as n,ft as o,St as p,zt as q,Jt as r,r as s,Tt as t,Et as u,fe as v,ce as w,re as x,pe as y,ie as z};
