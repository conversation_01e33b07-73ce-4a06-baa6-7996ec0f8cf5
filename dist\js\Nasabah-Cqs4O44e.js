import{f as e,r as a,j as s,U as t,F as n,h as r,G as l,H as i,l as d,k as o,i as c,I as m,J as x}from"./react-vendor-BH2w6qpU.js";import{C as h,c as p,a as u,b as g,d as j,B as b}from"./card-DfBXoe59.js";import{I as f}from"./input-CqUEtzg_.js";import{L as N}from"./label-CoNtBOlQ.js";import{T as v,a as y,b as k,c as w,d as S,e as D}from"./table-Cp0humNz.js";import{D as T,a as C,b as _,c as A,d as B,e as M,C as X}from"./ConfirmDialog-uGMzIHQA.js";import{D as P}from"./vendor-BGyKCyRD.js";import{A as F}from"./AdminSidebar-B0U-Ar4Y.js";import{S as I}from"./index-BSlVarex.js";import"./radix-vendor-CttiZxwU.js";import"./utils-vendor-DeZn2tlB.js";import"./query-vendor-B0Wv6VB8.js";const q=()=>{const q=e(),[E,J]=a.useState([]),[H,L]=a.useState(!0),[O,$]=a.useState(null),[K,G]=a.useState({name:"",email:"",phone:"",address:""}),[U,Y]=a.useState(!1),[z,R]=a.useState("add"),[W,Q]=a.useState(null),V=[{id:1,name:"Siti Nurhaliza",email:"<EMAIL>",phone:"081234567890",address:"Jl. Merdeka No. 123, Jakarta",total_points:1250,created_at:"2024-01-15"},{id:2,name:"Budi Santoso",email:"<EMAIL>",phone:"081234567891",address:"Jl. Sudirman No. 456, Bandung",total_points:890,created_at:"2024-02-10"},{id:3,name:"Ahmad Wijaya",email:"<EMAIL>",phone:"081234567892",address:"Jl. Gatot Subroto No. 789, Surabaya",total_points:2100,created_at:"2024-03-05"}];a.useEffect((()=>{const e=localStorage.getItem("user");if(!e)return void q("/login");if("admin"!==JSON.parse(e).role)return P.error("Akses Ditolak",{description:"Anda tidak memiliki akses ke halaman admin"}),void q("/");setTimeout((()=>{J(V),L(!1)}),1500)}),[q]);const Z=e=>{const{name:a,value:s}=e.target;G((e=>({...e,[a]:s})))};if(H)return s.jsxs("div",{className:"min-h-screen bg-gray-50 flex",children:[s.jsx(F,{}),s.jsx("div",{className:"flex-1 lg:ml-0 p-4 lg:p-8 pt-16 lg:pt-8",children:s.jsx(I,{type:"table"})})]});const ee={total:E.length,totalPoints:E.reduce(((e,a)=>e+a.total_points),0),avgPoints:E.length>0?Math.round(E.reduce(((e,a)=>e+a.total_points),0)/E.length):0,activeThisMonth:E.filter((e=>{const a=new Date(e.created_at),s=new Date;return a.getMonth()===s.getMonth()&&a.getFullYear()===s.getFullYear()})).length};return s.jsxs("div",{className:"min-h-screen bg-gray-50 flex",children:[s.jsx(F,{}),s.jsx("div",{className:"flex-1 lg:ml-0",children:s.jsxs("main",{className:"p-4 lg:p-8",children:[s.jsxs("div",{className:"mb-8",children:[s.jsxs("h2",{className:"text-2xl font-bold text-gray-800 mb-2 flex items-center",children:[s.jsx(t,{className:"w-6 h-6 mr-3 text-bank-green-600"}),"Kelola Nasabah"]}),s.jsx("p",{className:"text-gray-600",children:"Kelola data nasabah Bank Sampah Digital"})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[s.jsx(h,{className:"hover:shadow-lg transition-shadow duration-300",children:s.jsx(p,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Total Nasabah"}),s.jsx("p",{className:"text-2xl font-bold text-gray-900",children:ee.total})]}),s.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-bank-green-100 to-bank-green-200 rounded-xl flex items-center justify-center",children:s.jsx(t,{className:"w-6 h-6 text-bank-green-600"})})]})})}),s.jsx(h,{className:"hover:shadow-lg transition-shadow duration-300",children:s.jsx(p,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Total Poin"}),s.jsx("p",{className:"text-2xl font-bold text-purple-600",children:ee.totalPoints.toLocaleString()})]}),s.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-purple-100 to-purple-200 rounded-xl flex items-center justify-center",children:s.jsx(n,{className:"w-6 h-6 text-purple-600"})})]})})}),s.jsx(h,{className:"hover:shadow-lg transition-shadow duration-300",children:s.jsx(p,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Rata-rata Poin"}),s.jsx("p",{className:"text-2xl font-bold text-blue-600",children:ee.avgPoints})]}),s.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-100 to-blue-200 rounded-xl flex items-center justify-center",children:s.jsx(r,{className:"w-6 h-6 text-blue-600"})})]})})}),s.jsx(h,{className:"hover:shadow-lg transition-shadow duration-300",children:s.jsx(p,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Baru Bulan Ini"}),s.jsx("p",{className:"text-2xl font-bold text-orange-600",children:ee.activeThisMonth})]}),s.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-orange-100 to-orange-200 rounded-xl flex items-center justify-center",children:s.jsx(l,{className:"w-6 h-6 text-orange-600"})})]})})})]}),s.jsxs(h,{className:"shadow-xl border-0 bg-gradient-to-br from-white to-gray-50",children:[s.jsx(u,{className:"bg-gradient-to-r from-bank-green-50 to-bank-blue-50 rounded-t-lg border-b border-gray-100",children:s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsxs("div",{children:[s.jsxs(g,{className:"text-2xl font-bold text-gray-800 flex items-center gap-3",children:[s.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-bank-green-500 to-bank-green-600 rounded-xl flex items-center justify-center",children:s.jsx(t,{className:"w-5 h-5 text-white"})}),"Daftar Nasabah"]}),s.jsx(j,{className:"text-gray-600 mt-2",children:"Kelola dan pantau data nasabah Bank Sampah Digital"})]}),s.jsxs("button",{onClick:()=>{R("add"),G({name:"",email:"",phone:"",address:""}),Y(!0)},className:"btn-add group",children:[s.jsx(i,{className:"w-5 h-5 group-hover:rotate-90 transition-transform duration-300"}),"Tambah Nasabah"]})]})}),s.jsx(p,{className:"p-0",children:s.jsxs(v,{children:[s.jsx(y,{children:s.jsxs(k,{className:"bg-gray-50/50",children:[s.jsx(w,{className:"font-semibold text-gray-700",children:"Nama"}),s.jsx(w,{className:"font-semibold text-gray-700",children:"Email"}),s.jsx(w,{className:"font-semibold text-gray-700",children:"Telepon"}),s.jsx(w,{className:"font-semibold text-gray-700",children:"Alamat"}),s.jsx(w,{className:"font-semibold text-gray-700",children:"Total Poin"}),s.jsx(w,{className:"font-semibold text-gray-700",children:"Bergabung"}),s.jsx(w,{className:"font-semibold text-gray-700",children:"Aksi"})]})}),s.jsx(S,{children:E.map((e=>s.jsxs(k,{className:"hover:bg-gray-50/50 transition-colors",children:[s.jsx(D,{className:"font-medium text-gray-800",children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-bank-green-100 to-bank-green-200 rounded-full flex items-center justify-center",children:s.jsx("span",{className:"text-bank-green-700 font-semibold text-sm",children:e.name.charAt(0).toUpperCase()})}),e.name]})}),s.jsx(D,{className:"text-gray-600",children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(d,{className:"w-4 h-4 text-gray-400"}),e.email]})}),s.jsx(D,{className:"text-gray-600",children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(o,{className:"w-4 h-4 text-gray-400"}),e.phone]})}),s.jsx(D,{className:"text-gray-600 max-w-xs truncate",title:e.address,children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(c,{className:"w-4 h-4 text-gray-400"}),e.address]})}),s.jsx(D,{children:s.jsxs("span",{className:"bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800 px-3 py-1.5 rounded-full text-sm font-semibold shadow-sm flex items-center gap-1 w-fit",children:[s.jsx(n,{className:"w-3 h-3"}),e.total_points," poin"]})}),s.jsx(D,{className:"text-gray-600",children:new Date(e.created_at).toLocaleDateString("id-ID")}),s.jsx(D,{children:s.jsxs("div",{className:"flex space-x-2",children:[s.jsxs("button",{className:"btn-edit group flex items-center gap-2 px-3 py-2 text-sm rounded-lg",onClick:()=>(e=>{R("edit"),$(e),G({name:e.name,email:e.email,phone:e.phone,address:e.address}),Y(!0)})(e),title:"Edit Nasabah",children:[s.jsx(m,{className:"w-4 h-4 group-hover:scale-110 transition-transform duration-200"}),"Edit"]}),s.jsxs("button",{className:"btn-delete group flex items-center gap-2 px-3 py-2 text-sm rounded-lg",onClick:()=>(e=>{Q(e)})(e),title:"Hapus Nasabah",children:[s.jsx(x,{className:"w-4 h-4 group-hover:scale-110 transition-transform duration-200"}),"Hapus"]})]})})]},e.id)))})]})})]})]})}),s.jsx(T,{open:U,onOpenChange:Y,children:s.jsxs(C,{children:[s.jsxs(_,{children:[s.jsx(A,{children:"add"===z?"Tambah Nasabah Baru":"Edit Data Nasabah"}),s.jsx(B,{children:"add"===z?"Masukkan data nasabah baru":"Perbarui data nasabah"})]}),s.jsxs("form",{onSubmit:e=>{if(e.preventDefault(),"add"===z){const e={id:Math.max(...E.map((e=>e.id)))+1,...K,total_points:0,created_at:(new Date).toISOString().split("T")[0]};J((a=>[...a,e])),P.success("Nasabah baru berhasil ditambahkan!",{description:`${K.name} telah terdaftar sebagai nasabah`})}else if(O){const e={...O,...K};J((a=>a.map((a=>a.id===O.id?e:a)))),P.success("Data nasabah berhasil diperbarui!",{description:`Informasi ${K.name} telah diperbaharui`})}Y(!1),$(null)},className:"space-y-4",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsx(N,{htmlFor:"name",children:"Nama Lengkap"}),s.jsx(f,{id:"name",name:"name",value:K.name,onChange:Z,placeholder:"Masukkan nama lengkap",required:!0})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(N,{htmlFor:"email",children:"Email"}),s.jsx(f,{id:"email",name:"email",type:"email",value:K.email,onChange:Z,placeholder:"<EMAIL>",required:!0})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(N,{htmlFor:"phone",children:"No. Telepon"}),s.jsx(f,{id:"phone",name:"phone",value:K.phone,onChange:Z,placeholder:"08XXXXXXXXXX",required:!0})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(N,{htmlFor:"address",children:"Alamat"}),s.jsx("textarea",{id:"address",name:"address",value:K.address,onChange:Z,placeholder:"Masukkan alamat lengkap",className:"w-full px-3 py-2 border border-input bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 min-h-[80px] rounded-md",required:!0})]}),s.jsxs(M,{children:[s.jsx(b,{type:"button",variant:"outline",onClick:()=>Y(!1),children:"Batal"}),s.jsx(b,{type:"submit",className:"btn-primary",children:"add"===z?"Tambah":"Simpan"})]})]})]})}),s.jsx(X,{isOpen:!!W,onClose:()=>Q(null),onConfirm:()=>{W&&(J((e=>e.filter((e=>e.id!==W.id)))),P.success("Nasabah berhasil dihapus!",{description:`${W.name} telah dihapus dari sistem`}),Q(null))},title:"Hapus Nasabah",description:`Apakah Anda yakin ingin menghapus nasabah ${null==W?void 0:W.name}? Semua data transaksi dan poin yang terkait akan ikut terhapus. Tindakan ini tidak dapat dibatalkan.`,confirmText:"Hapus",cancelText:"Batal",type:"danger"})]})};export{q as default};
