var t,e,s,i,n,r,a,o,u,l,h,c,d,f,p,y,v,m,g,b,w,O,M,P,S,q,F,k,C,A,E,Q,D,x,R,W,K,T,j,U,H,G=t=>{throw TypeError(t)},L=(t,e,s)=>e.has(t)||G("Cannot "+s),I=(t,e,s)=>(L(t,e,"read from private field"),s?s.call(t):e.get(t)),_=(t,e,s)=>e.has(t)?G("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,s),N=(t,e,s,i)=>(L(t,e,"write to private field"),i?i.call(t,s):e.set(t,s),s),B=(t,e,s)=>(L(t,e,"access private method"),s),$=(t,e,s,i)=>({set _(i){N(t,e,i,s)},get _(){return I(t,e,i)}}),z=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},J="undefined"==typeof window||"Deno"in globalThis;function V(){}function X(t,e){return"function"==typeof t?t(e):t}function Y(t,e){const{type:s="all",exact:i,fetchStatus:n,predicate:r,queryKey:a,stale:o}=t;if(a)if(i){if(e.queryHash!==tt(a,e.options))return!1}else if(!st(e.queryKey,a))return!1;if("all"!==s){const t=e.isActive();if("active"===s&&!t)return!1;if("inactive"===s&&t)return!1}return("boolean"!=typeof o||e.isStale()===o)&&((!n||n===e.state.fetchStatus)&&!(r&&!r(e)))}function Z(t,e){const{exact:s,status:i,predicate:n,mutationKey:r}=t;if(r){if(!e.options.mutationKey)return!1;if(s){if(et(e.options.mutationKey)!==et(r))return!1}else if(!st(e.options.mutationKey,r))return!1}return(!i||e.state.status===i)&&!(n&&!n(e))}function tt(t,e){return((null==e?void 0:e.queryKeyHashFn)||et)(t)}function et(t){return JSON.stringify(t,((t,e)=>rt(e)?Object.keys(e).sort().reduce(((t,s)=>(t[s]=e[s],t)),{}):e))}function st(t,e){return t===e||typeof t==typeof e&&(!(!t||!e||"object"!=typeof t||"object"!=typeof e)&&Object.keys(e).every((s=>st(t[s],e[s]))))}function it(t,e){if(t===e)return t;const s=nt(t)&&nt(e);if(s||rt(t)&&rt(e)){const i=s?t:Object.keys(t),n=i.length,r=s?e:Object.keys(e),a=r.length,o=s?[]:{},u=new Set(i);let l=0;for(let h=0;h<a;h++){const i=s?h:r[h];(!s&&u.has(i)||s)&&void 0===t[i]&&void 0===e[i]?(o[i]=void 0,l++):(o[i]=it(t[i],e[i]),o[i]===t[i]&&void 0!==t[i]&&l++)}return n===a&&l===n?t:o}return e}function nt(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function rt(t){if(!at(t))return!1;const e=t.constructor;if(void 0===e)return!0;const s=e.prototype;return!!at(s)&&(!!s.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(t)===Object.prototype)}function at(t){return"[object Object]"===Object.prototype.toString.call(t)}function ot(t,e,s){return"function"==typeof s.structuralSharing?s.structuralSharing(t,e):!1!==s.structuralSharing?it(t,e):e}function ut(t,e,s=0){const i=[...t,e];return s&&i.length>s?i.slice(1):i}function lt(t,e,s=0){const i=[e,...t];return s&&i.length>s?i.slice(0,-1):i}var ht=Symbol();function ct(t,e){return!t.queryFn&&(null==e?void 0:e.initialPromise)?()=>e.initialPromise:t.queryFn&&t.queryFn!==ht?t.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`))}var dt=new(i=class extends z{constructor(){super(),_(this,t),_(this,e),_(this,s),N(this,s,(t=>{if(!J&&window.addEventListener){const e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}))}onSubscribe(){I(this,e)||this.setEventListener(I(this,s))}onUnsubscribe(){var t;this.hasListeners()||(null==(t=I(this,e))||t.call(this),N(this,e,void 0))}setEventListener(t){var i;N(this,s,t),null==(i=I(this,e))||i.call(this),N(this,e,t((t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})))}setFocused(e){I(this,t)!==e&&(N(this,t,e),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach((e=>{e(t)}))}isFocused(){var e;return"boolean"==typeof I(this,t)?I(this,t):"hidden"!==(null==(e=globalThis.document)?void 0:e.visibilityState)}},t=new WeakMap,e=new WeakMap,s=new WeakMap,i),ft=new(o=class extends z{constructor(){super(),_(this,n,!0),_(this,r),_(this,a),N(this,a,(t=>{if(!J&&window.addEventListener){const e=()=>t(!0),s=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",s)}}}))}onSubscribe(){I(this,r)||this.setEventListener(I(this,a))}onUnsubscribe(){var t;this.hasListeners()||(null==(t=I(this,r))||t.call(this),N(this,r,void 0))}setEventListener(t){var e;N(this,a,t),null==(e=I(this,r))||e.call(this),N(this,r,t(this.setOnline.bind(this)))}setOnline(t){I(this,n)!==t&&(N(this,n,t),this.listeners.forEach((e=>{e(t)})))}isOnline(){return I(this,n)}},n=new WeakMap,r=new WeakMap,a=new WeakMap,o);function pt(t){return Math.min(1e3*2**t,3e4)}function yt(t){return"online"!==(t??"online")||ft.isOnline()}var vt=class extends Error{constructor(t){super("CancelledError"),this.revert=null==t?void 0:t.revert,this.silent=null==t?void 0:t.silent}};function mt(t){return t instanceof vt}function gt(t){let e,s=!1,i=0,n=!1;const r=function(){let t,e;const s=new Promise(((s,i)=>{t=s,e=i}));function i(t){Object.assign(s,t),delete s.resolve,delete s.reject}return s.status="pending",s.catch((()=>{})),s.resolve=e=>{i({status:"fulfilled",value:e}),t(e)},s.reject=t=>{i({status:"rejected",reason:t}),e(t)},s}(),a=()=>dt.isFocused()&&("always"===t.networkMode||ft.isOnline())&&t.canRun(),o=()=>yt(t.networkMode)&&t.canRun(),u=s=>{var i;n||(n=!0,null==(i=t.onSuccess)||i.call(t,s),null==e||e(),r.resolve(s))},l=s=>{var i;n||(n=!0,null==(i=t.onError)||i.call(t,s),null==e||e(),r.reject(s))},h=()=>new Promise((s=>{var i;e=t=>{(n||a())&&s(t)},null==(i=t.onPause)||i.call(t)})).then((()=>{var s;e=void 0,n||null==(s=t.onContinue)||s.call(t)})),c=()=>{if(n)return;let e;const r=0===i?t.initialPromise:void 0;try{e=r??t.fn()}catch(o){e=Promise.reject(o)}Promise.resolve(e).then(u).catch((e=>{var r;if(n)return;const o=t.retry??(J?0:3),u=t.retryDelay??pt,d="function"==typeof u?u(i,e):u,f=!0===o||"number"==typeof o&&i<o||"function"==typeof o&&o(i,e);var p;!s&&f?(i++,null==(r=t.onFail)||r.call(t,i,e),(p=d,new Promise((t=>{setTimeout(t,p)}))).then((()=>a()?void 0:h())).then((()=>{s?l(e):c()}))):l(e)}))};return{promise:r,cancel:e=>{var s;n||(l(new vt(e)),null==(s=t.abort)||s.call(t))},continue:()=>(null==e||e(),r),cancelRetry:()=>{s=!0},continueRetry:()=>{s=!1},canStart:o,start:()=>(o()?c():h().then(c),r)}}var bt=t=>setTimeout(t,0);var wt=function(){let t=[],e=0,s=t=>{t()},i=t=>{t()},n=bt;const r=i=>{e?t.push(i):n((()=>{s(i)}))};return{batch:r=>{let a;e++;try{a=r()}finally{e--,e||(()=>{const e=t;t=[],e.length&&n((()=>{i((()=>{e.forEach((t=>{s(t)}))}))}))})()}return a},batchCalls:t=>(...e)=>{r((()=>{t(...e)}))},schedule:r,setNotifyFunction:t=>{s=t},setBatchNotifyFunction:t=>{i=t},setScheduler:t=>{n=t}}}(),Ot=(l=class{constructor(){_(this,u)}destroy(){this.clearGcTimeout()}scheduleGc(){var t;this.clearGcTimeout(),"number"==typeof(t=this.gcTime)&&t>=0&&t!==1/0&&N(this,u,setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(J?1/0:3e5))}clearGcTimeout(){I(this,u)&&(clearTimeout(I(this,u)),N(this,u,void 0))}},u=new WeakMap,l),Mt=(b=class extends Ot{constructor(t){super(),_(this,m),_(this,h),_(this,c),_(this,d),_(this,f),_(this,p),_(this,y),_(this,v),N(this,v,!1),N(this,y,t.defaultOptions),this.setOptions(t.options),this.observers=[],N(this,f,t.client),N(this,d,I(this,f).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,N(this,h,function(t){const e="function"==typeof t.initialData?t.initialData():t.initialData,s=void 0!==e,i=s?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:s?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}(this.options)),this.state=t.state??I(this,h),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return null==(t=I(this,p))?void 0:t.promise}setOptions(t){this.options={...I(this,y),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||I(this,d).remove(this)}setData(t,e){const s=ot(this.state.data,t,this.options);return B(this,m,g).call(this,{data:s,type:"success",dataUpdatedAt:null==e?void 0:e.updatedAt,manual:null==e?void 0:e.manual}),s}setState(t,e){B(this,m,g).call(this,{type:"setState",state:t,setStateOptions:e})}cancel(t){var e,s;const i=null==(e=I(this,p))?void 0:e.promise;return null==(s=I(this,p))||s.cancel(t),i?i.then(V).catch(V):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(I(this,h))}isActive(){return this.observers.some((t=>{return!1!==(e=t.options.enabled,s=this,"function"==typeof e?e(s):e);var e,s}))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===ht||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some((t=>"static"===X(t.options.staleTime,this)))}isStale(){return this.getObserversCount()>0?this.observers.some((t=>t.getCurrentResult().isStale)):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(t=0){return void 0===this.state.data||"static"!==t&&(!!this.state.isInvalidated||!function(t,e){return Math.max(t+(e||0)-Date.now(),0)}(this.state.dataUpdatedAt,t))}onFocus(){var t;const e=this.observers.find((t=>t.shouldFetchOnWindowFocus()));null==e||e.refetch({cancelRefetch:!1}),null==(t=I(this,p))||t.continue()}onOnline(){var t;const e=this.observers.find((t=>t.shouldFetchOnReconnect()));null==e||e.refetch({cancelRefetch:!1}),null==(t=I(this,p))||t.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),I(this,d).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter((e=>e!==t)),this.observers.length||(I(this,p)&&(I(this,v)?I(this,p).cancel({revert:!0}):I(this,p).cancelRetry()),this.scheduleGc()),I(this,d).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||B(this,m,g).call(this,{type:"invalidate"})}fetch(t,e){var s,i,n;if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&(null==e?void 0:e.cancelRefetch))this.cancel({silent:!0});else if(I(this,p))return I(this,p).continueRetry(),I(this,p).promise;if(t&&this.setOptions(t),!this.options.queryFn){const t=this.observers.find((t=>t.options.queryFn));t&&this.setOptions(t.options)}const r=new AbortController,a=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(N(this,v,!0),r.signal)})},o=()=>{const t=ct(this.options,e),s=(()=>{const t={client:I(this,f),queryKey:this.queryKey,meta:this.meta};return a(t),t})();return N(this,v,!1),this.options.persister?this.options.persister(t,s,this):t(s)},u=(()=>{const t={fetchOptions:e,options:this.options,queryKey:this.queryKey,client:I(this,f),state:this.state,fetchFn:o};return a(t),t})();null==(s=this.options.behavior)||s.onFetch(u,this),N(this,c,this.state),"idle"!==this.state.fetchStatus&&this.state.fetchMeta===(null==(i=u.fetchOptions)?void 0:i.meta)||B(this,m,g).call(this,{type:"fetch",meta:null==(n=u.fetchOptions)?void 0:n.meta});const l=t=>{var e,s,i,n;mt(t)&&t.silent||B(this,m,g).call(this,{type:"error",error:t}),mt(t)||(null==(s=(e=I(this,d).config).onError)||s.call(e,t,this),null==(n=(i=I(this,d).config).onSettled)||n.call(i,this.state.data,t,this)),this.scheduleGc()};return N(this,p,gt({initialPromise:null==e?void 0:e.initialPromise,fn:u.fetchFn,abort:r.abort.bind(r),onSuccess:t=>{var e,s,i,n;if(void 0!==t){try{this.setData(t)}catch(r){return void l(r)}null==(s=(e=I(this,d).config).onSuccess)||s.call(e,t,this),null==(n=(i=I(this,d).config).onSettled)||n.call(i,t,this.state.error,this),this.scheduleGc()}else l(new Error(`${this.queryHash} data is undefined`))},onError:l,onFail:(t,e)=>{B(this,m,g).call(this,{type:"failed",failureCount:t,error:e})},onPause:()=>{B(this,m,g).call(this,{type:"pause"})},onContinue:()=>{B(this,m,g).call(this,{type:"continue"})},retry:u.options.retry,retryDelay:u.options.retryDelay,networkMode:u.options.networkMode,canRun:()=>!0})),I(this,p).start()}},h=new WeakMap,c=new WeakMap,d=new WeakMap,f=new WeakMap,p=new WeakMap,y=new WeakMap,v=new WeakMap,m=new WeakSet,g=function(t){this.state=(e=>{switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,...(s=e.data,i=this.options,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:yt(i.networkMode)?"fetching":"paused",...void 0===s&&{error:null,status:"pending"}}),fetchMeta:t.meta??null};case"success":return{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const n=t.error;return mt(n)&&n.revert&&I(this,c)?{...I(this,c),fetchStatus:"idle"}:{...e,error:n,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:n,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}var s,i})(this.state),wt.batch((()=>{this.observers.forEach((t=>{t.onQueryUpdate()})),I(this,d).notify({query:this,type:"updated",action:t})}))},b);var Pt=(O=class extends z{constructor(t={}){super(),_(this,w),this.config=t,N(this,w,new Map)}build(t,e,s){const i=e.queryKey,n=e.queryHash??tt(i,e);let r=this.get(n);return r||(r=new Mt({client:t,queryKey:i,queryHash:n,options:t.defaultQueryOptions(e),state:s,defaultOptions:t.getQueryDefaults(i)}),this.add(r)),r}add(t){I(this,w).has(t.queryHash)||(I(this,w).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const e=I(this,w).get(t.queryHash);e&&(t.destroy(),e===t&&I(this,w).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){wt.batch((()=>{this.getAll().forEach((t=>{this.remove(t)}))}))}get(t){return I(this,w).get(t)}getAll(){return[...I(this,w).values()]}find(t){const e={exact:!0,...t};return this.getAll().find((t=>Y(e,t)))}findAll(t={}){const e=this.getAll();return Object.keys(t).length>0?e.filter((e=>Y(t,e))):e}notify(t){wt.batch((()=>{this.listeners.forEach((e=>{e(t)}))}))}onFocus(){wt.batch((()=>{this.getAll().forEach((t=>{t.onFocus()}))}))}onOnline(){wt.batch((()=>{this.getAll().forEach((t=>{t.onOnline()}))}))}},w=new WeakMap,O),St=(k=class extends Ot{constructor(t){super(),_(this,q),_(this,M),_(this,P),_(this,S),this.mutationId=t.mutationId,N(this,P,t.mutationCache),N(this,M,[]),this.state=t.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){I(this,M).includes(t)||(I(this,M).push(t),this.clearGcTimeout(),I(this,P).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){N(this,M,I(this,M).filter((e=>e!==t))),this.scheduleGc(),I(this,P).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){I(this,M).length||("pending"===this.state.status?this.scheduleGc():I(this,P).remove(this))}continue(){var t;return(null==(t=I(this,S))?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var e,s,i,n,r,a,o,u,l,h,c,d,f,p,y,v,m,g,b,w;const O=()=>{B(this,q,F).call(this,{type:"continue"})};N(this,S,gt({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(t,e)=>{B(this,q,F).call(this,{type:"failed",failureCount:t,error:e})},onPause:()=>{B(this,q,F).call(this,{type:"pause"})},onContinue:O,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>I(this,P).canRun(this)}));const M="pending"===this.state.status,k=!I(this,S).canStart();try{if(M)O();else{B(this,q,F).call(this,{type:"pending",variables:t,isPaused:k}),await(null==(s=(e=I(this,P).config).onMutate)?void 0:s.call(e,t,this));const r=await(null==(n=(i=this.options).onMutate)?void 0:n.call(i,t));r!==this.state.context&&B(this,q,F).call(this,{type:"pending",context:r,variables:t,isPaused:k})}const f=await I(this,S).start();return await(null==(a=(r=I(this,P).config).onSuccess)?void 0:a.call(r,f,t,this.state.context,this)),await(null==(u=(o=this.options).onSuccess)?void 0:u.call(o,f,t,this.state.context)),await(null==(h=(l=I(this,P).config).onSettled)?void 0:h.call(l,f,null,this.state.variables,this.state.context,this)),await(null==(d=(c=this.options).onSettled)?void 0:d.call(c,f,null,t,this.state.context)),B(this,q,F).call(this,{type:"success",data:f}),f}catch(C){try{throw await(null==(p=(f=I(this,P).config).onError)?void 0:p.call(f,C,t,this.state.context,this)),await(null==(v=(y=this.options).onError)?void 0:v.call(y,C,t,this.state.context)),await(null==(g=(m=I(this,P).config).onSettled)?void 0:g.call(m,void 0,C,this.state.variables,this.state.context,this)),await(null==(w=(b=this.options).onSettled)?void 0:w.call(b,void 0,C,t,this.state.context)),C}finally{B(this,q,F).call(this,{type:"error",error:C})}}finally{I(this,P).runNext(this)}}},M=new WeakMap,P=new WeakMap,S=new WeakMap,q=new WeakSet,F=function(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),wt.batch((()=>{I(this,M).forEach((e=>{e.onMutationUpdate(t)})),I(this,P).notify({mutation:this,type:"updated",action:t})}))},k);var qt=(Q=class extends z{constructor(t={}){super(),_(this,C),_(this,A),_(this,E),this.config=t,N(this,C,new Set),N(this,A,new Map),N(this,E,0)}build(t,e,s){const i=new St({mutationCache:this,mutationId:++$(this,E)._,options:t.defaultMutationOptions(e),state:s});return this.add(i),i}add(t){I(this,C).add(t);const e=Ft(t);if("string"==typeof e){const s=I(this,A).get(e);s?s.push(t):I(this,A).set(e,[t])}this.notify({type:"added",mutation:t})}remove(t){if(I(this,C).delete(t)){const e=Ft(t);if("string"==typeof e){const s=I(this,A).get(e);if(s)if(s.length>1){const e=s.indexOf(t);-1!==e&&s.splice(e,1)}else s[0]===t&&I(this,A).delete(e)}}this.notify({type:"removed",mutation:t})}canRun(t){const e=Ft(t);if("string"==typeof e){const s=I(this,A).get(e),i=null==s?void 0:s.find((t=>"pending"===t.state.status));return!i||i===t}return!0}runNext(t){var e;const s=Ft(t);if("string"==typeof s){const i=null==(e=I(this,A).get(s))?void 0:e.find((e=>e!==t&&e.state.isPaused));return(null==i?void 0:i.continue())??Promise.resolve()}return Promise.resolve()}clear(){wt.batch((()=>{I(this,C).forEach((t=>{this.notify({type:"removed",mutation:t})})),I(this,C).clear(),I(this,A).clear()}))}getAll(){return Array.from(I(this,C))}find(t){const e={exact:!0,...t};return this.getAll().find((t=>Z(e,t)))}findAll(t={}){return this.getAll().filter((e=>Z(t,e)))}notify(t){wt.batch((()=>{this.listeners.forEach((e=>{e(t)}))}))}resumePausedMutations(){const t=this.getAll().filter((t=>t.state.isPaused));return wt.batch((()=>Promise.all(t.map((t=>t.continue().catch(V))))))}},C=new WeakMap,A=new WeakMap,E=new WeakMap,Q);function Ft(t){var e;return null==(e=t.options.scope)?void 0:e.id}function kt(t){return{onFetch:(e,s)=>{var i,n,r,a,o;const u=e.options,l=null==(r=null==(n=null==(i=e.fetchOptions)?void 0:i.meta)?void 0:n.fetchMore)?void 0:r.direction,h=(null==(a=e.state.data)?void 0:a.pages)||[],c=(null==(o=e.state.data)?void 0:o.pageParams)||[];let d={pages:[],pageParams:[]},f=0;const p=async()=>{let s=!1;const i=ct(e.options,e.fetchOptions),n=async(t,n,r)=>{if(s)return Promise.reject();if(null==n&&t.pages.length)return Promise.resolve(t);const a=(()=>{const t={client:e.client,queryKey:e.queryKey,pageParam:n,direction:r?"backward":"forward",meta:e.options.meta};var i;return i=t,Object.defineProperty(i,"signal",{enumerable:!0,get:()=>(e.signal.aborted?s=!0:e.signal.addEventListener("abort",(()=>{s=!0})),e.signal)}),t})(),o=await i(a),{maxPages:u}=e.options,l=r?lt:ut;return{pages:l(t.pages,o,u),pageParams:l(t.pageParams,n,u)}};if(l&&h.length){const t="backward"===l,e={pages:h,pageParams:c},s=(t?At:Ct)(u,e);d=await n(e,s,t)}else{const e=t??h.length;do{const t=0===f?c[0]??u.initialPageParam:Ct(u,d);if(f>0&&null==t)break;d=await n(d,t),f++}while(f<e)}return d};e.options.persister?e.fetchFn=()=>{var t,i;return null==(i=(t=e.options).persister)?void 0:i.call(t,p,{client:e.client,queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},s)}:e.fetchFn=p}}}function Ct(t,{pages:e,pageParams:s}){const i=e.length-1;return e.length>0?t.getNextPageParam(e[i],e,s[i],s):void 0}function At(t,{pages:e,pageParams:s}){var i;return e.length>0?null==(i=t.getPreviousPageParam)?void 0:i.call(t,e[0],e,s[0],s):void 0}var Et=(H=class{constructor(t={}){_(this,D),_(this,x),_(this,R),_(this,W),_(this,K),_(this,T),_(this,j),_(this,U),N(this,D,t.queryCache||new Pt),N(this,x,t.mutationCache||new qt),N(this,R,t.defaultOptions||{}),N(this,W,new Map),N(this,K,new Map),N(this,T,0)}mount(){$(this,T)._++,1===I(this,T)&&(N(this,j,dt.subscribe((async t=>{t&&(await this.resumePausedMutations(),I(this,D).onFocus())}))),N(this,U,ft.subscribe((async t=>{t&&(await this.resumePausedMutations(),I(this,D).onOnline())}))))}unmount(){var t,e;$(this,T)._--,0===I(this,T)&&(null==(t=I(this,j))||t.call(this),N(this,j,void 0),null==(e=I(this,U))||e.call(this),N(this,U,void 0))}isFetching(t){return I(this,D).findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return I(this,x).findAll({...t,status:"pending"}).length}getQueryData(t){var e;const s=this.defaultQueryOptions({queryKey:t});return null==(e=I(this,D).get(s.queryHash))?void 0:e.state.data}ensureQueryData(t){const e=this.defaultQueryOptions(t),s=I(this,D).build(this,e),i=s.state.data;return void 0===i?this.fetchQuery(t):(t.revalidateIfStale&&s.isStaleByTime(X(e.staleTime,s))&&this.prefetchQuery(e),Promise.resolve(i))}getQueriesData(t){return I(this,D).findAll(t).map((({queryKey:t,state:e})=>[t,e.data]))}setQueryData(t,e,s){const i=this.defaultQueryOptions({queryKey:t}),n=I(this,D).get(i.queryHash),r=function(t,e){return"function"==typeof t?t(e):t}(e,null==n?void 0:n.state.data);if(void 0!==r)return I(this,D).build(this,i).setData(r,{...s,manual:!0})}setQueriesData(t,e,s){return wt.batch((()=>I(this,D).findAll(t).map((({queryKey:t})=>[t,this.setQueryData(t,e,s)]))))}getQueryState(t){var e;const s=this.defaultQueryOptions({queryKey:t});return null==(e=I(this,D).get(s.queryHash))?void 0:e.state}removeQueries(t){const e=I(this,D);wt.batch((()=>{e.findAll(t).forEach((t=>{e.remove(t)}))}))}resetQueries(t,e){const s=I(this,D);return wt.batch((()=>(s.findAll(t).forEach((t=>{t.reset()})),this.refetchQueries({type:"active",...t},e))))}cancelQueries(t,e={}){const s={revert:!0,...e},i=wt.batch((()=>I(this,D).findAll(t).map((t=>t.cancel(s)))));return Promise.all(i).then(V).catch(V)}invalidateQueries(t,e={}){return wt.batch((()=>(I(this,D).findAll(t).forEach((t=>{t.invalidate()})),"none"===(null==t?void 0:t.refetchType)?Promise.resolve():this.refetchQueries({...t,type:(null==t?void 0:t.refetchType)??(null==t?void 0:t.type)??"active"},e))))}refetchQueries(t,e={}){const s={...e,cancelRefetch:e.cancelRefetch??!0},i=wt.batch((()=>I(this,D).findAll(t).filter((t=>!t.isDisabled()&&!t.isStatic())).map((t=>{let e=t.fetch(void 0,s);return s.throwOnError||(e=e.catch(V)),"paused"===t.state.fetchStatus?Promise.resolve():e}))));return Promise.all(i).then(V)}fetchQuery(t){const e=this.defaultQueryOptions(t);void 0===e.retry&&(e.retry=!1);const s=I(this,D).build(this,e);return s.isStaleByTime(X(e.staleTime,s))?s.fetch(e):Promise.resolve(s.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(V).catch(V)}fetchInfiniteQuery(t){return t.behavior=kt(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(V).catch(V)}ensureInfiniteQueryData(t){return t.behavior=kt(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return ft.isOnline()?I(this,x).resumePausedMutations():Promise.resolve()}getQueryCache(){return I(this,D)}getMutationCache(){return I(this,x)}getDefaultOptions(){return I(this,R)}setDefaultOptions(t){N(this,R,t)}setQueryDefaults(t,e){I(this,W).set(et(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){const e=[...I(this,W).values()],s={};return e.forEach((e=>{st(t,e.queryKey)&&Object.assign(s,e.defaultOptions)})),s}setMutationDefaults(t,e){I(this,K).set(et(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){const e=[...I(this,K).values()],s={};return e.forEach((e=>{st(t,e.mutationKey)&&Object.assign(s,e.defaultOptions)})),s}defaultQueryOptions(t){if(t._defaulted)return t;const e={...I(this,R).queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=tt(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.throwOnError&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.queryFn===ht&&(e.enabled=!1),e}defaultMutationOptions(t){return(null==t?void 0:t._defaulted)?t:{...I(this,R).mutations,...(null==t?void 0:t.mutationKey)&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){I(this,D).clear(),I(this,x).clear()}},D=new WeakMap,x=new WeakMap,R=new WeakMap,W=new WeakMap,K=new WeakMap,T=new WeakMap,j=new WeakMap,U=new WeakMap,H);export{Et as Q};
