import{f as e,r as s,j as a,ak as t,ap as r,y as l,I as i,aq as n,ar as c,n as d,w as o}from"./react-vendor-C9kdeAq4.js";import{N as m}from"./NasabahSidebar-Daz452gA.js";import{S as x,e as p}from"./index-BZfOZLOv.js";import{B as h,C as u,c as g,a as j,b as f}from"./card-l7WlC16I.js";import{B as N}from"./badge-8i3m94Oh.js";import{I as w}from"./input-B25hGi49.js";import"./radix-vendor-CttiZxwU.js";import"./vendor-C0DTsUaw.js";import"./utils-vendor-DeZn2tlB.js";import"./query-vendor-B0Wv6VB8.js";const y=()=>{const y=e(),[v,b]=s.useState(!0),[k,T]=s.useState(""),[S,P]=s.useState("all"),[C,D]=s.useState("desc"),[L]=s.useState([{id:"TRX001",date:"2024-06-14",type:"pickup",description:"Penjemputan Sampah Plastik",points:150,status:"completed",waste_type:"Plastik",weight:5.2},{id:"TRX002",date:"2024-06-12",type:"exchange",description:"Tukar Poin - Pulsa Rp 10.000",points:-100,status:"completed",reward_item:"Pulsa Rp 10.000"},{id:"TRX003",date:"2024-06-10",type:"pickup",description:"Penjemputan Sampah Kertas",points:80,status:"pending",waste_type:"Kertas",weight:3.1},{id:"TRX004",date:"2024-06-08",type:"pickup",description:"Penjemputan Sampah Logam",points:200,status:"completed",waste_type:"Logam",weight:2.5},{id:"TRX005",date:"2024-06-05",type:"exchange",description:"Tukar Poin - Voucher Belanja",points:-250,status:"completed",reward_item:"Voucher Belanja Rp 25.000"}]);s.useEffect((()=>{(async()=>{b(!0),await new Promise((e=>setTimeout(e,1e3)));const e=localStorage.getItem("user");if(!e)return void y("/login");"nasabah"===JSON.parse(e).role?b(!1):y("/login")})()}),[y]);const R=e=>{switch(e){case"completed":return"bg-green-100 text-green-800 border-green-200";case"pending":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"cancelled":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},_=e=>{switch(e){case"completed":return"Selesai";case"pending":return"Menunggu";case"cancelled":return"Dibatalkan";default:return e}},B=e=>"pickup"===e?"bg-blue-100 text-blue-600":"bg-purple-100 text-purple-600",I=L.filter((e=>{const s=e.description.toLowerCase().includes(k.toLowerCase())||e.id.toLowerCase().includes(k.toLowerCase()),a="all"===S||e.type===S;return s&&a})).sort(((e,s)=>{const a=new Date(e.date).getTime(),t=new Date(s.date).getTime();return"desc"===C?t-a:a-t})),X=L.filter((e=>"completed"===e.status)).reduce(((e,s)=>e+s.points),0),A=L.filter((e=>"completed"===e.status)).length,E=L.filter((e=>"pending"===e.status)).length;return v?a.jsxs("div",{className:"min-h-screen bg-gray-50",children:[a.jsx(m,{}),a.jsx("div",{className:"lg:ml-64",children:a.jsx("main",{className:"p-4 pt-16 lg:pt-8",children:a.jsx(x,{type:"riwayat"})})})]}):a.jsxs("div",{className:"min-h-screen bg-gray-50",children:[a.jsx(m,{}),a.jsx("div",{className:"lg:ml-64",children:a.jsxs("main",{className:"p-4 pt-16 lg:pt-8 space-y-6",children:[a.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[a.jsxs("div",{children:[a.jsxs("h1",{className:"text-2xl lg:text-3xl font-bold text-gray-800 flex items-center",children:[a.jsx(t,{className:"w-8 h-8 mr-3 text-bank-green-600"}),"Riwayat Transaksi"]}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Lihat semua aktivitas dan transaksi Anda"})]}),a.jsxs(h,{variant:"outline",onClick:()=>p.info("Fitur Export",{description:"Akan segera tersedia"}),className:"hover-scale",children:[a.jsx(r,{className:"w-4 h-4 mr-2"}),"Export Data"]})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[a.jsx(u,{className:"hover:shadow-lg transition-shadow duration-300",children:a.jsx(g,{className:"p-6",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Poin Earned"}),a.jsxs("p",{className:"text-2xl font-bold text-green-600",children:["+",X.toLocaleString()]})]}),a.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center",children:a.jsx(l,{className:"w-6 h-6 text-green-600"})})]})})}),a.jsx(u,{className:"hover:shadow-lg transition-shadow duration-300",children:a.jsx(g,{className:"p-6",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Transaksi Selesai"}),a.jsx("p",{className:"text-2xl font-bold text-blue-600",children:A})]}),a.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center",children:a.jsx(t,{className:"w-6 h-6 text-blue-600"})})]})})}),a.jsx(u,{className:"hover:shadow-lg transition-shadow duration-300",children:a.jsx(g,{className:"p-6",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Menunggu Proses"}),a.jsx("p",{className:"text-2xl font-bold text-yellow-600",children:E})]}),a.jsx("div",{className:"w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center",children:a.jsx(i,{className:"w-6 h-6 text-yellow-600"})})]})})})]}),a.jsxs(u,{children:[a.jsx(j,{children:a.jsx(f,{children:"Filter & Pencarian"})}),a.jsx(g,{children:a.jsxs("div",{className:"flex flex-col lg:flex-row gap-4",children:[a.jsx("div",{className:"flex-1",children:a.jsxs("div",{className:"relative",children:[a.jsx(n,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),a.jsx(w,{placeholder:"Cari berdasarkan ID atau deskripsi...",value:k,onChange:e=>T(e.target.value),className:"pl-10"})]})}),a.jsxs("div",{className:"flex gap-2",children:[a.jsxs("select",{value:S,onChange:e=>P(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-bank-green-500 focus:border-transparent",children:[a.jsx("option",{value:"all",children:"Semua Tipe"}),a.jsx("option",{value:"pickup",children:"Penjemputan"}),a.jsx("option",{value:"exchange",children:"Tukar Poin"})]}),a.jsxs(h,{variant:"outline",onClick:()=>D("desc"===C?"asc":"desc"),className:"hover-scale",children:[a.jsx(c,{className:"w-4 h-4 mr-2"}),"desc"===C?"Terbaru":"Terlama"]})]})]})})]}),a.jsxs(u,{children:[a.jsx(j,{children:a.jsxs(f,{children:["Daftar Transaksi (",I.length,")"]})}),a.jsx(g,{children:0===I.length?a.jsxs("div",{className:"text-center py-12",children:[a.jsx(t,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-600 mb-2",children:"Tidak ada transaksi"}),a.jsx("p",{className:"text-gray-500",children:"Belum ada transaksi yang sesuai dengan filter Anda"})]}):a.jsx("div",{className:"space-y-4",children:I.map((e=>{const s="pickup"===e.type?o:l;var t;return a.jsxs("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-200",children:[a.jsxs("div",{className:"flex items-center space-x-4",children:[a.jsx("div",{className:`w-12 h-12 rounded-full flex items-center justify-center ${B(e.type)}`,children:a.jsx(s,{className:"w-6 h-6"})}),a.jsxs("div",{children:[a.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[a.jsx("h4",{className:"font-medium text-gray-800",children:e.description}),a.jsx(N,{className:R(e.status),children:_(e.status)})]}),a.jsxs("p",{className:"text-sm text-gray-500",children:[e.id," • ",(t=e.date,new Date(t).toLocaleDateString("id-ID",{day:"numeric",month:"long",year:"numeric"}))]}),e.waste_type&&a.jsxs("p",{className:"text-sm text-gray-600",children:[e.waste_type," • ",e.weight," kg"]}),e.reward_item&&a.jsx("p",{className:"text-sm text-gray-600",children:e.reward_item})]})]}),a.jsxs("div",{className:"text-right",children:[a.jsxs("p",{className:"text-lg font-bold "+(e.points>0?"text-green-600":"text-red-600"),children:[e.points>0?"+":"",e.points.toLocaleString()," Poin"]}),a.jsxs(h,{variant:"ghost",size:"sm",onClick:()=>p.info("Detail Transaksi",{description:"Akan segera tersedia"}),className:"mt-2 hover-scale",children:[a.jsx(d,{className:"w-4 h-4 mr-1"}),"Detail"]})]})]},e.id)}))})})]})]})})]})};export{y as default};
