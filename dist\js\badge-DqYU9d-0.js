import{j as r}from"./react-vendor-BH2w6qpU.js";import{a as e}from"./utils-vendor-DeZn2tlB.js";import{c as t}from"./index-BSlVarex.js";const o=e("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n({className:e,variant:n,...a}){return r.jsx("div",{className:t(o({variant:n}),e),...a})}export{n as B};
