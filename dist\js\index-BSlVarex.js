const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/Index-BnHfr37_.js","js/react-vendor-BH2w6qpU.js","js/radix-vendor-CttiZxwU.js","js/vendor-BGyKCyRD.js","js/card-DfBXoe59.js","js/utils-vendor-DeZn2tlB.js","js/badge-DqYU9d-0.js","js/query-vendor-B0Wv6VB8.js","js/Login-DRm6TlBf.js","js/input-CqUEtzg_.js","js/label-CoNtBOlQ.js","js/checkbox-DSbm822M.js","js/Register-CMMBBHfH.js","js/Dashboard-CM3WI-4w.js","js/AdminSidebar-B0U-Ar4Y.js","js/Nasabah-Cqs4O44e.js","js/table-Cp0humNz.js","js/ConfirmDialog-uGMzIHQA.js","js/Kategori-TWu1VXQ5.js","js/PenjemputanSampah-MSmgo_-a.js","js/Transaksi-Dkb1aRvo.js","js/Dashboard-DdrDLleT.js","js/NasabahSidebar-DWU2nAjI.js","js/Profil-JKA8AJWt.js","js/textarea-DKFbvSZZ.js","js/RiwayatTransaksi-9TenBppq.js","js/RequestJemput-G8bg2HPm.js","js/TukarPoin-l-5wTIxP.js"])))=>i.map(i=>d[i]);
import{r as s,j as e,V as a,R as r,A as l,C as t,X as c,T as d,D as i,P as n,a as o,b as m,u as x,Q as p,B as j,c as h,d as u,e as N}from"./react-vendor-BH2w6qpU.js";import{t as g,c as f,a as y}from"./utils-vendor-DeZn2tlB.js";import{$ as v}from"./vendor-BGyKCyRD.js";import{Q as b}from"./query-vendor-B0Wv6VB8.js";import"./radix-vendor-CttiZxwU.js";!function(){const s=document.createElement("link").relList;if(!(s&&s.supports&&s.supports("modulepreload"))){for(const s of document.querySelectorAll('link[rel="modulepreload"]'))e(s);new MutationObserver((s=>{for(const a of s)if("childList"===a.type)for(const s of a.addedNodes)"LINK"===s.tagName&&"modulepreload"===s.rel&&e(s)})).observe(document,{childList:!0,subtree:!0})}function e(s){if(s.ep)return;s.ep=!0;const e=function(s){const e={};return s.integrity&&(e.integrity=s.integrity),s.referrerPolicy&&(e.referrerPolicy=s.referrerPolicy),"use-credentials"===s.crossOrigin?e.credentials="include":"anonymous"===s.crossOrigin?e.credentials="omit":e.credentials="same-origin",e}(s);fetch(s.href,e)}}();const w={},_=function(s,e,a){let r=Promise.resolve();if(e&&e.length>0){document.getElementsByTagName("link");const s=document.querySelector("meta[property=csp-nonce]"),a=(null==s?void 0:s.nonce)||(null==s?void 0:s.getAttribute("nonce"));r=Promise.allSettled(e.map((s=>{if((s=function(s){return"/"+s}(s))in w)return;w[s]=!0;const e=s.endsWith(".css"),r=e?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${s}"]${r}`))return;const l=document.createElement("link");return l.rel=e?"stylesheet":"modulepreload",e||(l.as="script"),l.crossOrigin="",l.href=s,a&&l.setAttribute("nonce",a),document.head.appendChild(l),e?new Promise(((e,a)=>{l.addEventListener("load",e),l.addEventListener("error",(()=>a(new Error(`Unable to preload CSS for ${s}`))))})):void 0})))}function l(s){const e=new Event("vite:preloadError",{cancelable:!0});if(e.payload=s,window.dispatchEvent(e),!e.defaultPrevented)throw s}return r.then((e=>{for(const s of e||[])"rejected"===s.status&&l(s.reason);return s().catch(l)}))};let E=0;const A=new Map,S=s=>{if(A.has(s))return;const e=setTimeout((()=>{A.delete(s),I({type:"REMOVE_TOAST",toastId:s})}),3e3);A.set(s,e)},T=(s,e)=>{switch(e.type){case"ADD_TOAST":return{...s,toasts:[e.toast,...s.toasts].slice(0,1)};case"UPDATE_TOAST":return{...s,toasts:s.toasts.map((s=>s.id===e.toast.id?{...s,...e.toast}:s))};case"DISMISS_TOAST":{const{toastId:a}=e;return a?S(a):s.toasts.forEach((s=>{S(s.id)})),{...s,toasts:s.toasts.map((s=>s.id===a||void 0===a?{...s,open:!1}:s))}}case"REMOVE_TOAST":return void 0===e.toastId?{...s,toasts:[]}:{...s,toasts:s.toasts.filter((s=>s.id!==e.toastId))}}},O=[];let k={toasts:[]};function I(s){k=T(k,s),O.forEach((s=>{s(k)}))}function R({...s}){const e=(E=(E+1)%Number.MAX_SAFE_INTEGER,E.toString()),a=()=>I({type:"DISMISS_TOAST",toastId:e});return I({type:"ADD_TOAST",toast:{...s,id:e,open:!0,onOpenChange:s=>{s||a()}}}),{id:e,dismiss:a,update:s=>I({type:"UPDATE_TOAST",toast:{...s,id:e}})}}const P={success:(s,e)=>R({title:s,description:null==e?void 0:e.description,variant:"default",className:"bg-green-50 border-green-200 text-green-800"}),error:(s,e)=>R({title:s,description:null==e?void 0:e.description,variant:"destructive",className:"bg-red-50 border-red-200 text-red-800"}),info:(s,e)=>R({title:s,description:null==e?void 0:e.description,variant:"default",className:"bg-blue-50 border-blue-200 text-blue-800"}),warning:(s,e)=>R({title:s,description:null==e?void 0:e.description,variant:"default",className:"bg-yellow-50 border-yellow-200 text-yellow-800"})},D=Object.assign(R,P);function L(...s){return g(f(s))}const z=n,V=s.forwardRef((({className:s,...r},l)=>e.jsx(a,{ref:l,className:L("fixed top-4 right-4 z-[100] flex max-h-screen w-full flex-col p-4 md:max-w-[420px]",s),...r})));V.displayName=a.displayName;const M=y("group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),q=s.forwardRef((({className:s,variant:a,...l},t)=>e.jsx(r,{ref:t,className:L(M({variant:a}),s),...l})));q.displayName=r.displayName;s.forwardRef((({className:s,...a},r)=>e.jsx(l,{ref:r,className:L("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium transition-colors hover:bg-secondary focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",s),...a}))).displayName=l.displayName;const B=s.forwardRef((({className:s,...a},r)=>e.jsx(t,{ref:r,className:L("absolute right-1 top-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",s),"toast-close":"",...a,children:e.jsx(c,{className:"h-4 w-4"})})));B.displayName=t.displayName;const C=s.forwardRef((({className:s,...a},r)=>e.jsx(d,{ref:r,className:L("text-sm font-semibold [&+div]:text-xs",s),...a})));C.displayName=d.displayName;const $=s.forwardRef((({className:s,...a},r)=>e.jsx(i,{ref:r,className:L("text-sm opacity-90",s),...a})));function U(){const{toasts:a}=function(){const[e,a]=s.useState(k);return s.useEffect((()=>(O.push(a),()=>{const s=O.indexOf(a);s>-1&&O.splice(s,1)})),[e]),{...e,toast:R,dismiss:s=>I({type:"DISMISS_TOAST",toastId:s})}}();return e.jsxs(z,{children:[a.map((function({id:s,title:a,description:r,action:l,...t}){return e.jsxs(q,{...t,children:[e.jsxs("div",{className:"grid gap-1",children:[a&&e.jsx(C,{children:a}),r&&e.jsx($,{children:r})]}),l,e.jsx(B,{})]},s)})),e.jsx(V,{})]})}$.displayName=i.displayName;const K=({...s})=>e.jsx(v,{position:"top-right",duration:3e3,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-white group-[.toaster]:text-gray-900 group-[.toaster]:border group-[.toaster]:shadow-lg group-[.toaster]:rounded-lg",description:"group-[.toast]:text-gray-600",actionButton:"group-[.toast]:bg-bank-green-600 group-[.toast]:text-white group-[.toast]:hover:bg-bank-green-700",cancelButton:"group-[.toast]:bg-gray-100 group-[.toast]:text-gray-600 group-[.toast]:hover:bg-gray-200",success:"group-[.toast]:border-bank-green-200 group-[.toast]:bg-bank-green-50",error:"group-[.toast]:border-red-200 group-[.toast]:bg-red-50",info:"group-[.toast]:border-blue-200 group-[.toast]:bg-blue-50",warning:"group-[.toast]:border-yellow-200 group-[.toast]:bg-yellow-50"}},...s}),Q=m;function F({className:s,...a}){return e.jsx("div",{className:L("animate-pulse rounded-md bg-primary/10",s),...a})}s.forwardRef((({className:s,sideOffset:a=4,...r},l)=>e.jsx(o,{ref:l,sideOffset:a,className:L("z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...r}))).displayName=o.displayName;const G=({type:s="dashboard",count:a=1})=>e.jsx("div",{className:"animate-pulse",children:(()=>{switch(s){case"dashboard":return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(F,{className:"h-8 w-48"}),e.jsx(F,{className:"h-4 w-96"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[...Array(4)].map(((s,a)=>e.jsx("div",{className:"p-6 border rounded-lg space-y-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(F,{className:"h-4 w-24"}),e.jsx(F,{className:"h-8 w-16"}),e.jsx(F,{className:"h-4 w-20"})]}),e.jsx(F,{className:"h-12 w-12 rounded-xl"})]})},a)))}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsx("div",{className:"lg:col-span-2 space-y-6",children:e.jsxs("div",{className:"p-6 border rounded-lg space-y-4",children:[e.jsx(F,{className:"h-6 w-32"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[...Array(4)].map(((s,a)=>e.jsx("div",{className:"p-4 border rounded-lg space-y-3",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(F,{className:"h-10 w-10 rounded-lg"}),e.jsxs("div",{className:"space-y-2 flex-1",children:[e.jsx(F,{className:"h-4 w-24"}),e.jsx(F,{className:"h-3 w-32"})]})]})},a)))})]})}),e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"p-6 border rounded-lg space-y-4",children:[e.jsx(F,{className:"h-6 w-28"}),e.jsx("div",{className:"space-y-3",children:[...Array(4)].map(((s,a)=>e.jsxs("div",{className:"flex items-center space-x-3 p-3 border rounded-lg",children:[e.jsx(F,{className:"h-8 w-8 rounded-full"}),e.jsxs("div",{className:"flex-1 space-y-2",children:[e.jsx(F,{className:"h-4 w-24"}),e.jsx(F,{className:"h-3 w-16"})]})]},a)))})]})})]})]});case"card":return e.jsx("div",{className:"p-6 border rounded-lg space-y-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(F,{className:"h-4 w-24"}),e.jsx(F,{className:"h-8 w-16"})]}),e.jsx(F,{className:"h-12 w-12 rounded-xl"})]})});case"table":return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(F,{className:"h-6 w-32"}),e.jsx(F,{className:"h-9 w-24"})]}),e.jsxs("div",{className:"border rounded-lg",children:[e.jsx("div",{className:"grid grid-cols-4 gap-4 p-4 border-b",children:[...Array(4)].map(((s,a)=>e.jsx(F,{className:"h-4 w-20"},a)))}),[...Array(5)].map(((s,a)=>e.jsx("div",{className:"grid grid-cols-4 gap-4 p-4 border-b last:border-b-0",children:[...Array(4)].map(((s,a)=>e.jsx(F,{className:"h-4 w-16"},a)))},a)))]})]});case"form":return e.jsxs("div",{className:"space-y-4",children:[e.jsx(F,{className:"h-6 w-32"}),[...Array(a)].map(((s,a)=>e.jsxs("div",{className:"space-y-2",children:[e.jsx(F,{className:"h-4 w-20"}),e.jsx(F,{className:"h-10 w-full"})]},a))),e.jsx(F,{className:"h-10 w-24"})]});case"profile":return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(F,{className:"h-8 w-48"}),e.jsx(F,{className:"h-4 w-96"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsx("div",{className:"lg:col-span-2",children:e.jsxs("div",{className:"p-6 border rounded-lg space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(F,{className:"h-6 w-32"}),e.jsx(F,{className:"h-4 w-48"})]}),e.jsx(F,{className:"h-10 w-24"})]}),e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsx(F,{className:"h-24 w-24 rounded-full"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(F,{className:"h-6 w-32"}),e.jsx(F,{className:"h-4 w-48"}),e.jsx(F,{className:"h-6 w-16"})]})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[...Array(6)].map(((s,a)=>e.jsxs("div",{className:"space-y-2",children:[e.jsx(F,{className:"h-4 w-20"}),e.jsx(F,{className:"h-10 w-full"})]},a)))})]})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"p-6 border rounded-lg space-y-4",children:[e.jsx(F,{className:"h-6 w-28"}),e.jsx("div",{className:"space-y-4",children:[...Array(3)].map(((s,a)=>e.jsx("div",{className:"p-3 border rounded-lg space-y-2",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx(F,{className:"h-4 w-20"}),e.jsx(F,{className:"h-6 w-16"})]}),e.jsx(F,{className:"h-8 w-8"})]})},a)))})]}),e.jsxs("div",{className:"p-6 border rounded-lg space-y-4",children:[e.jsx(F,{className:"h-6 w-24"}),e.jsx("div",{className:"space-y-3",children:[...Array(3)].map(((s,a)=>e.jsx(F,{className:"h-10 w-full"},a)))})]})]})]})]});case"tukar-poin":return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(F,{className:"h-8 w-48"}),e.jsx(F,{className:"h-4 w-96"})]}),e.jsxs("div",{className:"flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit",children:[e.jsx(F,{className:"h-8 w-24"}),e.jsx(F,{className:"h-8 w-32"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map(((s,a)=>e.jsxs("div",{className:"p-6 border rounded-lg space-y-4",children:[e.jsx(F,{className:"h-32 w-full rounded-lg"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(F,{className:"h-5 w-32"}),e.jsx(F,{className:"h-4 w-24"}),e.jsx(F,{className:"h-4 w-20"})]}),e.jsx(F,{className:"h-10 w-full"})]},a)))})]});case"riwayat":return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(F,{className:"h-8 w-48"}),e.jsx(F,{className:"h-4 w-96"})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[e.jsx(F,{className:"h-10 w-48"}),e.jsx(F,{className:"h-10 w-32"}),e.jsx(F,{className:"h-10 w-24"})]}),e.jsxs("div",{className:"border rounded-lg",children:[e.jsx("div",{className:"grid grid-cols-5 gap-4 p-4 border-b",children:[...Array(5)].map(((s,a)=>e.jsx(F,{className:"h-4 w-20"},a)))}),[...Array(8)].map(((s,a)=>e.jsx("div",{className:"grid grid-cols-5 gap-4 p-4 border-b last:border-b-0",children:[...Array(5)].map(((s,a)=>e.jsx(F,{className:"h-4 w-16"},a)))},a)))]})]});case"request-jemput":return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(F,{className:"h-8 w-64"}),e.jsx(F,{className:"h-4 w-96"})]}),e.jsxs("div",{className:"flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit",children:[e.jsx(F,{className:"h-8 w-32"}),e.jsx(F,{className:"h-8 w-24"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsxs("div",{className:"p-6 border rounded-lg space-y-6",children:[e.jsx("div",{className:"space-y-2",children:e.jsx(F,{className:"h-6 w-48"})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(F,{className:"h-4 w-32"}),e.jsx(F,{className:"h-10 w-full"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(F,{className:"h-4 w-32"}),e.jsx(F,{className:"h-10 w-full"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(F,{className:"h-4 w-32"}),e.jsx(F,{className:"h-20 w-full"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(F,{className:"h-4 w-24"}),e.jsx(F,{className:"h-10 w-full"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(F,{className:"h-4 w-20"}),e.jsx(F,{className:"h-20 w-full"})]})]}),e.jsxs("div",{className:"p-6 border rounded-lg space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(F,{className:"h-6 w-32"}),e.jsx(F,{className:"h-8 w-24"})]}),e.jsxs("div",{className:"p-4 border rounded-lg space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(F,{className:"h-4 w-20"}),e.jsx(F,{className:"h-10 w-full"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(F,{className:"h-4 w-16"}),e.jsx(F,{className:"h-10 w-full"})]}),e.jsx("div",{className:"flex items-end",children:e.jsx(F,{className:"h-8 w-8"})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(F,{className:"h-4 w-20"}),e.jsx(F,{className:"h-16 w-full"})]})]})]})]}),e.jsx("div",{children:e.jsxs("div",{className:"p-6 border rounded-lg space-y-4",children:[e.jsx(F,{className:"h-6 w-32"}),e.jsx("div",{className:"space-y-3",children:[...Array(4)].map(((s,a)=>e.jsxs("div",{className:"flex justify-between",children:[e.jsx(F,{className:"h-4 w-16"}),e.jsx(F,{className:"h-4 w-20"})]},a)))}),e.jsx("div",{className:"pt-4 border-t",children:e.jsx(F,{className:"h-10 w-full"})}),e.jsxs("div",{className:"p-3 bg-blue-50 rounded-lg",children:[e.jsx(F,{className:"h-3 w-full mb-2"}),e.jsx(F,{className:"h-3 w-3/4"})]})]})})]})]});default:return e.jsx(F,{className:"h-20 w-full"})}})()}),H=()=>{const a=x();return s.useEffect((()=>{}),[a.pathname]),e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-4xl font-bold mb-4",children:"404"}),e.jsx("p",{className:"text-xl text-gray-600 mb-4",children:"Oops! Page not found"}),e.jsx("a",{href:"/",className:"text-blue-500 hover:text-blue-700 underline",children:"Return to Home"})]})})},J=s.lazy((()=>_((()=>import("./Index-BnHfr37_.js")),__vite__mapDeps([0,1,2,3,4,5,6,7])))),W=s.lazy((()=>_((()=>import("./Login-DRm6TlBf.js")),__vite__mapDeps([8,1,2,3,4,5,9,10,11,7])))),X=s.lazy((()=>_((()=>import("./Register-CMMBBHfH.js")),__vite__mapDeps([12,1,2,3,4,5,9,10,11,7])))),Y=s.lazy((()=>_((()=>import("./Dashboard-CM3WI-4w.js")),__vite__mapDeps([13,1,2,3,4,5,6,14,7])))),Z=s.lazy((()=>_((()=>import("./Nasabah-Cqs4O44e.js")),__vite__mapDeps([15,1,2,3,4,5,9,10,16,17,14,7])))),ss=s.lazy((()=>_((()=>import("./Kategori-TWu1VXQ5.js")),__vite__mapDeps([18,1,2,3,4,5,9,10,16,17,14,7])))),es=s.lazy((()=>_((()=>import("./PenjemputanSampah-MSmgo_-a.js")),__vite__mapDeps([19,1,2,3,4,5,9,10,16,17,14,7])))),as=s.lazy((()=>_((()=>import("./Transaksi-Dkb1aRvo.js")),__vite__mapDeps([20,1,2,3,4,5,9,10,16,17,6,14,7])))),rs=s.lazy((()=>_((()=>import("./Dashboard-DdrDLleT.js")),__vite__mapDeps([21,1,2,3,4,5,6,22,7])))),ls=s.lazy((()=>_((()=>import("./Profil-JKA8AJWt.js")),__vite__mapDeps([23,1,2,3,4,5,9,10,24,6,22,17,7])))),ts=s.lazy((()=>_((()=>import("./RiwayatTransaksi-9TenBppq.js")),__vite__mapDeps([25,1,2,3,22,4,5,6,9,7])))),cs=s.lazy((()=>_((()=>import("./RequestJemput-G8bg2HPm.js")),__vite__mapDeps([26,1,2,3,22,4,5,6,9,10,24,17,7])))),ds=s.lazy((()=>_((()=>import("./TukarPoin-l-5wTIxP.js")),__vite__mapDeps([27,1,2,3,22,4,5,6,9,17,7])))),is=new b,ns=({type:s="dashboard"})=>e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsx(G,{type:s})}),os=()=>e.jsx(p,{client:is,children:e.jsxs(Q,{children:[e.jsx(U,{}),e.jsx(K,{}),e.jsx(j,{children:e.jsx(s.Suspense,{fallback:e.jsx(ns,{}),children:e.jsxs(h,{children:[e.jsx(u,{path:"/",element:e.jsx(J,{})}),e.jsx(u,{path:"/login",element:e.jsx(s.Suspense,{fallback:e.jsx(ns,{type:"form"}),children:e.jsx(W,{})})}),e.jsx(u,{path:"/register",element:e.jsx(s.Suspense,{fallback:e.jsx(ns,{type:"form"}),children:e.jsx(X,{})})}),e.jsx(u,{path:"/admin/dashboard",element:e.jsx(s.Suspense,{fallback:e.jsx(ns,{type:"dashboard"}),children:e.jsx(Y,{})})}),e.jsx(u,{path:"/admin/nasabah",element:e.jsx(s.Suspense,{fallback:e.jsx(ns,{type:"table"}),children:e.jsx(Z,{})})}),e.jsx(u,{path:"/admin/kategori",element:e.jsx(s.Suspense,{fallback:e.jsx(ns,{type:"table"}),children:e.jsx(ss,{})})}),e.jsx(u,{path:"/admin/penjemputan",element:e.jsx(s.Suspense,{fallback:e.jsx(ns,{type:"table"}),children:e.jsx(es,{})})}),e.jsx(u,{path:"/admin/transaksi",element:e.jsx(s.Suspense,{fallback:e.jsx(ns,{type:"table"}),children:e.jsx(as,{})})}),e.jsx(u,{path:"/nasabah/dashboard",element:e.jsx(s.Suspense,{fallback:e.jsx(ns,{type:"dashboard"}),children:e.jsx(rs,{})})}),e.jsx(u,{path:"/nasabah/profil",element:e.jsx(s.Suspense,{fallback:e.jsx(ns,{type:"profile"}),children:e.jsx(ls,{})})}),e.jsx(u,{path:"/nasabah/riwayat",element:e.jsx(s.Suspense,{fallback:e.jsx(ns,{type:"riwayat"}),children:e.jsx(ts,{})})}),e.jsx(u,{path:"/nasabah/jemput",element:e.jsx(s.Suspense,{fallback:e.jsx(ns,{type:"request-jemput"}),children:e.jsx(cs,{})})}),e.jsx(u,{path:"/nasabah/tukar-poin",element:e.jsx(s.Suspense,{fallback:e.jsx(ns,{type:"tukar-poin"}),children:e.jsx(ds,{})})}),e.jsx(u,{path:"*",element:e.jsx(H,{})})]})})})]})});N(document.getElementById("root")).render(e.jsx(os,{}));export{G as S,L as c,D as e};
