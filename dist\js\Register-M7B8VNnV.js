import{f as e,r as a,j as s,m as r,o as n,E as t,n as i}from"./react-vendor-C9kdeAq4.js";import{B as l,C as o,a as d,b as m,d as c,c as u}from"./card-l7WlC16I.js";import{I as x}from"./input-B25hGi49.js";import{L as h}from"./label-ZSTvXwUz.js";import{C as g}from"./checkbox-BPqGXD6n.js";import{e as p}from"./index-BZfOZLOv.js";import"./radix-vendor-CttiZxwU.js";import"./vendor-C0DTsUaw.js";import"./utils-vendor-DeZn2tlB.js";import"./query-vendor-B0Wv6VB8.js";const b=()=>{const b=e(),[f,j]=a.useState({name:"",email:"",password:"",confirmPassword:"",phone:"",address:"",agreeToTerms:!1}),[k,N]=a.useState(!1),[y,v]=a.useState(!1),[w,P]=a.useState(!1),C=e=>{const{name:a,value:s}=e.target;j((e=>({...e,[a]:s})))};return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-bank-green-50 to-bank-blue-50 flex items-center justify-center p-4",children:s.jsxs("div",{className:"w-full max-w-md",children:[s.jsxs(l,{variant:"ghost",onClick:()=>b("/"),className:"mb-6 hover:bg-white/50",children:[s.jsx(r,{className:"w-4 h-4 mr-2"}),"Kembali ke Beranda"]}),s.jsxs(o,{className:"w-full shadow-2xl border-0 bg-white/90 backdrop-blur-sm animate-bounce-in",children:[s.jsxs(d,{className:"text-center space-y-4",children:[s.jsx("div",{className:"w-16 h-16 mx-auto bg-gradient-green rounded-2xl flex items-center justify-center",children:s.jsx(n,{className:"w-8 h-8 text-white"})}),s.jsxs("div",{children:[s.jsx(m,{className:"text-2xl font-bold text-gray-800",children:"Daftar Sebagai Nasabah"}),s.jsx(c,{className:"text-gray-600",children:"Bergabunglah dengan Bank Sampah Digital dan mulai kontribusi untuk lingkungan"})]})]}),s.jsxs(u,{className:"space-y-6",children:[s.jsxs("form",{onSubmit:async e=>{e.preventDefault(),f.name&&f.email&&f.password&&f.phone&&f.address?f.password===f.confirmPassword?f.password.length<6?p.error("Password Terlalu Pendek",{description:"Password minimal 6 karakter"}):f.agreeToTerms?(P(!0),await new Promise((e=>setTimeout(e,2e3))),p.success("Registrasi Berhasil! 🎉",{description:"Akun nasabah Anda telah dibuat. Silakan login untuk melanjutkan."}),P(!1),b("/login")):p.error("Persetujuan Diperlukan",{description:"Anda harus menyetujui syarat dan ketentuan"}):p({title:"Password Tidak Cocok",description:"Password dan konfirmasi password harus sama",variant:"destructive"}):p({title:"Data Tidak Lengkap",description:"Mohon lengkapi semua field yang wajib diisi",variant:"destructive"})},className:"space-y-4",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsx(h,{htmlFor:"name",className:"text-sm font-medium text-gray-700",children:"Nama Lengkap *"}),s.jsx(x,{id:"name",name:"name",type:"text",placeholder:"Masukkan nama lengkap",value:f.name,onChange:C,className:"w-full transition-all duration-200 focus:ring-2 focus:ring-bank-green-500",required:!0})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(h,{htmlFor:"email",className:"text-sm font-medium text-gray-700",children:"Email *"}),s.jsx(x,{id:"email",name:"email",type:"email",placeholder:"<EMAIL>",value:f.email,onChange:C,className:"w-full transition-all duration-200 focus:ring-2 focus:ring-bank-green-500",required:!0})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(h,{htmlFor:"phone",className:"text-sm font-medium text-gray-700",children:"No. Telepon *"}),s.jsx(x,{id:"phone",name:"phone",type:"tel",placeholder:"08XXXXXXXXXX",value:f.phone,onChange:C,className:"w-full transition-all duration-200 focus:ring-2 focus:ring-bank-green-500",required:!0})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(h,{htmlFor:"address",className:"text-sm font-medium text-gray-700",children:"Alamat *"}),s.jsx("textarea",{id:"address",name:"address",placeholder:"Masukkan alamat lengkap",value:f.address,onChange:C,className:"w-full px-3 py-2 border border-input bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 min-h-[80px] rounded-md transition-all duration-200 focus:ring-2 focus:ring-bank-green-500",required:!0})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(h,{htmlFor:"password",className:"text-sm font-medium text-gray-700",children:"Password *"}),s.jsxs("div",{className:"relative",children:[s.jsx(x,{id:"password",name:"password",type:k?"text":"password",placeholder:"Minimal 6 karakter",value:f.password,onChange:C,className:"w-full pr-10 transition-all duration-200 focus:ring-2 focus:ring-bank-green-500",required:!0}),s.jsx("button",{type:"button",onClick:()=>N(!k),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700",children:k?s.jsx(t,{className:"w-4 h-4"}):s.jsx(i,{className:"w-4 h-4"})})]})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(h,{htmlFor:"confirmPassword",className:"text-sm font-medium text-gray-700",children:"Konfirmasi Password *"}),s.jsxs("div",{className:"relative",children:[s.jsx(x,{id:"confirmPassword",name:"confirmPassword",type:y?"text":"password",placeholder:"Ulangi password",value:f.confirmPassword,onChange:C,className:"w-full pr-10 transition-all duration-200 focus:ring-2 focus:ring-bank-green-500",required:!0}),s.jsx("button",{type:"button",onClick:()=>v(!y),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700",children:y?s.jsx(t,{className:"w-4 h-4"}):s.jsx(i,{className:"w-4 h-4"})})]})]}),s.jsxs("div",{className:"flex items-start space-x-2",children:[s.jsx(g,{id:"agreeToTerms",checked:f.agreeToTerms,onCheckedChange:e=>j((a=>({...a,agreeToTerms:!!e})))}),s.jsxs(h,{htmlFor:"agreeToTerms",className:"text-sm text-gray-600 leading-relaxed",children:["Saya menyetujui"," ",s.jsx("button",{type:"button",className:"text-bank-green-600 hover:text-bank-green-700 font-medium hover:underline",onClick:()=>p.info("Syarat & Ketentuan",{description:"Halaman akan tersedia segera"}),children:"Syarat & Ketentuan"})," ","dan"," ",s.jsx("button",{type:"button",className:"text-bank-green-600 hover:text-bank-green-700 font-medium hover:underline",onClick:()=>p.info("Kebijakan Privasi",{description:"Halaman akan tersedia segera"}),children:"Kebijakan Privasi"})]})]}),s.jsx(l,{type:"submit",disabled:w,className:"w-full btn-primary py-3 text-base font-medium",children:w?"Memproses...":"Daftar Sekarang"})]}),s.jsxs("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-0 flex items-center",children:s.jsx("span",{className:"w-full border-t border-gray-300"})}),s.jsx("div",{className:"relative flex justify-center text-xs uppercase",children:s.jsx("span",{className:"bg-white px-2 text-gray-500",children:"atau"})})]}),s.jsx("div",{className:"text-center space-y-2",children:s.jsxs("p",{className:"text-sm text-gray-600",children:["Sudah punya akun?"," ",s.jsx("button",{onClick:()=>b("/login"),className:"text-bank-green-600 hover:text-bank-green-700 font-medium hover:underline transition-colors",children:"Masuk di sini"})]})}),s.jsxs("div",{className:"text-xs text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200",children:[s.jsx("p",{className:"font-medium mb-1 text-blue-700",children:"📌 Informasi Penting:"}),s.jsxs("p",{className:"text-blue-600",children:["Pendaftaran ini khusus untuk ",s.jsx("strong",{children:"Nasabah"}),". Akun Admin dikelola secara terpisah oleh sistem."]})]})]})]})]})})};export{b as default};
