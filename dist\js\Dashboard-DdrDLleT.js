import{r as e,j as s,aj as a,ak as i,f as r,F as t,g as l,h as n,al as c,y as d,am as m,an as x,ae as o}from"./react-vendor-BH2w6qpU.js";import{C as h,c as j,a as p,b as g,d as u,B as N}from"./card-DfBXoe59.js";import{B as f}from"./badge-DqYU9d-0.js";import{c as v,S as y}from"./index-BSlVarex.js";import{D as b}from"./vendor-BGyKCyRD.js";import{N as k}from"./NasabahSidebar-DWU2nAjI.js";import"./radix-vendor-CttiZxwU.js";import"./utils-vendor-DeZn2tlB.js";import"./query-vendor-B0Wv6VB8.js";const w=e.forwardRef((({className:e,value:r,...t},l)=>s.jsx(a,{ref:l,className:v("relative h-2 w-full overflow-hidden rounded-full bg-primary/20",e),...t,children:s.jsx(i,{className:"h-full w-full flex-1 bg-gradient-to-r from-bank-green-500 to-bank-green-600 transition-all",style:{transform:`translateX(-${100-(r||0)}%)`}})})));w.displayName=a.displayName;const K=()=>{const a=r(),[i,v]=e.useState(null),[K,T]=e.useState(!0);e.useEffect((()=>{(async()=>{T(!0),await new Promise((e=>setTimeout(e,1500)));const e=localStorage.getItem("user");if(!e)return void a("/login");const s=JSON.parse(e);if("nasabah"!==s.role)return b.error("Akses Ditolak",{description:"Anda tidak memiliki akses ke halaman nasabah"}),void a("/");v(s),T(!1)})()}),[a]);const P=1250,S="45.2 Kg",R=18,A="Silver",C=500,F=P%1e3/1e3*100;return K?s.jsxs("div",{className:"min-h-screen bg-gray-50 flex",children:[s.jsx(k,{}),s.jsx("div",{className:"flex-1 ml-0 lg:ml-64 p-8 pt-16 lg:pt-8",children:s.jsx(y,{type:"dashboard"})})]}):i?s.jsxs("div",{className:"min-h-screen bg-gray-50 flex",children:[s.jsx(k,{}),s.jsx("div",{className:"flex-1 ml-0 lg:ml-64",children:s.jsxs("main",{className:"p-4 pt-16 lg:p-8 lg:pt-8",children:[s.jsxs("div",{className:"mb-8 animate-fade-in",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Selamat datang kembali! 👋"}),s.jsx("p",{className:"text-gray-600",children:"Mari kita lihat progress lingkungan Anda hari ini."})]}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[s.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[s.jsx(h,{className:"hover:shadow-lg transition-shadow duration-300 hover-scale",children:s.jsx(j,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Total Poin"}),s.jsx("p",{className:"text-2xl font-bold text-bank-green-600",children:P})]}),s.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center",children:s.jsx(t,{className:"w-6 h-6 text-green-600"})})]})})}),s.jsx(h,{className:"hover:shadow-lg transition-shadow duration-300 hover-scale",children:s.jsx(j,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Sampah Terkumpul"}),s.jsx("p",{className:"text-2xl font-bold text-blue-600",children:S})]}),s.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center",children:s.jsx(l,{className:"w-6 h-6 text-blue-600"})})]})})}),s.jsx(h,{className:"hover:shadow-lg transition-shadow duration-300 hover-scale",children:s.jsx(j,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Total Transaksi"}),s.jsx("p",{className:"text-2xl font-bold text-purple-600",children:R})]}),s.jsx("div",{className:"w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center",children:s.jsx(n,{className:"w-6 h-6 text-purple-600"})})]})})})]}),s.jsxs(h,{children:[s.jsx(p,{children:s.jsxs(g,{className:"flex items-center",children:[s.jsx(c,{className:"w-5 h-5 mr-2 text-yellow-600"}),"Rank Progress"]})}),s.jsxs(j,{children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(f,{variant:"secondary",className:"bg-gray-200 text-gray-800",children:A}),s.jsxs("span",{className:"text-sm text-gray-600",children:[C," poin lagi ke Gold"]})]}),s.jsxs("span",{className:"text-sm font-medium text-gray-800",children:[Math.round(F),"%"]})]}),s.jsx(w,{value:F,className:"h-3 bg-gray-200"})]})]}),s.jsxs(h,{children:[s.jsxs(p,{children:[s.jsx(g,{children:"Aksi Cepat"}),s.jsx(u,{children:"Fitur-fitur yang sering digunakan"})]}),s.jsx(j,{children:s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs(N,{className:"h-auto p-4 justify-start hover-scale",variant:"outline",onClick:()=>b.info("Fitur Request Jemput",{description:"Akan segera tersedia"}),children:[s.jsx(d,{className:"w-5 h-5 mr-3 text-orange-600"}),s.jsxs("div",{className:"text-left",children:[s.jsx("div",{className:"font-medium",children:"Request Jemput"}),s.jsx("div",{className:"text-sm text-gray-500",children:"Jadwalkan penjemputan sampah"})]})]}),s.jsxs(N,{className:"h-auto p-4 justify-start hover-scale",variant:"outline",onClick:()=>b.info("Fitur Riwayat Transaksi",{description:"Akan segera tersedia"}),children:[s.jsx(m,{className:"w-5 h-5 mr-3 text-blue-600"}),s.jsxs("div",{className:"text-left",children:[s.jsx("div",{className:"font-medium",children:"Riwayat Transaksi"}),s.jsx("div",{className:"text-sm text-gray-500",children:"Lihat transaksi sebelumnya"})]})]}),s.jsxs(N,{className:"h-auto p-4 justify-start hover-scale",variant:"outline",onClick:()=>b.info("Fitur Tukar Poin",{description:"Akan segera tersedia"}),children:[s.jsx(x,{className:"w-5 h-5 mr-3 text-purple-600"}),s.jsxs("div",{className:"text-left",children:[s.jsx("div",{className:"font-medium",children:"Tukar Poin"}),s.jsx("div",{className:"text-sm text-gray-500",children:"Gunakan poin untuk hadiah"})]})]}),s.jsxs(N,{className:"h-auto p-4 justify-start hover-scale",variant:"outline",onClick:()=>b.info("Fitur Update Profil",{description:"Akan segera tersedia"}),children:[s.jsx(o,{className:"w-5 h-5 mr-3 text-green-600"}),s.jsxs("div",{className:"text-left",children:[s.jsx("div",{className:"font-medium",children:"Update Profil"}),s.jsx("div",{className:"text-sm text-gray-500",children:"Kelola informasi akun"})]})]})]})})]})]}),s.jsxs("div",{className:"space-y-6",children:[s.jsxs(h,{children:[s.jsx(p,{children:s.jsxs(g,{className:"flex items-center",children:[s.jsx(c,{className:"w-5 h-5 mr-2 text-yellow-600"}),"Pencapaian"]})}),s.jsx(j,{children:s.jsx("div",{className:"space-y-3",children:[{title:"First Timer",description:"Transaksi pertama",earned:!0,icon:"🎉"},{title:"Eco Warrior",description:"50+ Kg sampah terkumpul",earned:!0,icon:"🌱"},{title:"Point Master",description:"1000+ poin dikumpulkan",earned:!0,icon:"⭐"},{title:"Green Champion",description:"100+ Kg sampah terkumpul",earned:!1,icon:"🏆"}].map(((e,a)=>s.jsxs("div",{className:"flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 "+(e.earned?"bg-green-50 border border-green-200":"bg-gray-50"),children:[s.jsx("span",{className:"text-2xl",children:e.icon}),s.jsxs("div",{className:"flex-1",children:[s.jsx("h4",{className:"font-medium "+(e.earned?"text-green-800":"text-gray-600"),children:e.title}),s.jsx("p",{className:"text-xs "+(e.earned?"text-green-600":"text-gray-500"),children:e.description})]}),e.earned&&s.jsx(f,{variant:"secondary",className:"bg-green-100 text-green-800 text-xs",children:"✓"})]},a)))})})]}),s.jsxs(h,{children:[s.jsxs(p,{children:[s.jsx(g,{children:"Daftar Harga Sampah"}),s.jsx(u,{children:"Harga dan poin per kilogram"})]}),s.jsx(j,{children:s.jsx("div",{className:"space-y-3",children:[{name:"Plastik PET",price:"Rp 3.000/Kg",points:"30 poin/Kg",icon:"🥤"},{name:"Kertas Kardus",price:"Rp 2.000/Kg",points:"20 poin/Kg",icon:"📦"},{name:"Botol Kaca",price:"Rp 5.000/Kg",points:"50 poin/Kg",icon:"🍺"},{name:"Kaleng Aluminium",price:"Rp 8.000/Kg",points:"80 poin/Kg",icon:"🥫"}].map(((e,a)=>s.jsxs("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("span",{className:"text-2xl",children:e.icon}),s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium text-gray-800",children:e.name}),s.jsx("p",{className:"text-sm text-gray-500",children:e.price})]})]}),s.jsx(f,{variant:"secondary",className:"bg-bank-green-100 text-bank-green-800",children:e.points})]},a)))})})]}),s.jsxs(h,{children:[s.jsx(p,{children:s.jsxs(g,{className:"flex items-center",children:[s.jsx(m,{className:"w-5 h-5 mr-2 text-blue-600"}),"Transaksi Terbaru"]})}),s.jsx(j,{children:s.jsx("div",{className:"space-y-3",children:[{id:1,date:"2024-01-15",type:"Plastik PET",weight:"2.5 Kg",points:75,status:"completed"},{id:2,date:"2024-01-12",type:"Kertas Kardus",weight:"5.0 Kg",points:100,status:"completed"},{id:3,date:"2024-01-10",type:"Botol Kaca",weight:"3.2 Kg",points:160,status:"completed"}].map((e=>s.jsxs("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium text-gray-800",children:e.type}),s.jsxs("p",{className:"text-sm text-gray-500",children:[e.weight," • ",e.date]})]}),s.jsxs("div",{className:"text-right",children:[s.jsxs("p",{className:"font-medium text-bank-green-600",children:["+",e.points," poin"]}),s.jsx(f,{variant:"secondary",className:"bg-green-100 text-green-800 text-xs",children:"Selesai"})]})]},e.id)))})})]})]})]})]})})]}):null};export{K as default};
