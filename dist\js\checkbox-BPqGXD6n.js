import{r as e,j as s,p as r,q as a,s as i}from"./react-vendor-C9kdeAq4.js";import{c as o}from"./index-BZfOZLOv.js";const t=e.forwardRef((({className:e,...t},d)=>s.jsx(r,{ref:d,className:o("peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:s.jsx(a,{className:o("flex items-center justify-center text-current"),children:s.jsx(i,{className:"h-4 w-4"})})})));t.displayName=r.displayName;export{t as C};
