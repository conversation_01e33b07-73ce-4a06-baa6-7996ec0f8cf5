const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/Index-C8OZQZiF.js","js/react-vendor-C9kdeAq4.js","js/radix-vendor-CttiZxwU.js","js/vendor-C0DTsUaw.js","js/card-l7WlC16I.js","js/utils-vendor-DeZn2tlB.js","js/badge-8i3m94Oh.js","js/query-vendor-B0Wv6VB8.js","js/Login-CA_6-jVY.js","js/input-B25hGi49.js","js/label-ZSTvXwUz.js","js/checkbox-BPqGXD6n.js","js/Register-M7B8VNnV.js","js/Dashboard-CbCztPv-.js","js/AdminSidebar-y32hu_7f.js","js/Nasabah-CR4li4jx.js","js/table-qPaxNgV0.js","js/ConfirmDialog-KlxVkBeS.js","js/Kategori-CwuodaWH.js","js/PenjemputanSampah-B36w3vMN.js","js/Transaksi-DNSD5_6w.js","js/Dashboard-C_5S1YLn.js","js/NasabahSidebar-Daz452gA.js","js/Profil-l0PBmkQI.js","js/textarea-Bb34uEIZ.js","js/RiwayatTransaksi-D5L7d9EQ.js","js/RequestJemput-Cr4l5n-6.js","js/TukarPoin-DDZKeJfs.js"])))=>i.map(i=>d[i]);
import{r as s,j as e,V as a,R as r,A as l,C as c,X as t,T as d,D as i,P as n,a as o,b as m,u as x,Q as p,B as j,c as h,d as u,e as N}from"./react-vendor-C9kdeAq4.js";import{t as f,c as y,a as g}from"./utils-vendor-DeZn2tlB.js";import{Q as v}from"./query-vendor-B0Wv6VB8.js";import"./radix-vendor-CttiZxwU.js";import"./vendor-C0DTsUaw.js";!function(){const s=document.createElement("link").relList;if(!(s&&s.supports&&s.supports("modulepreload"))){for(const s of document.querySelectorAll('link[rel="modulepreload"]'))e(s);new MutationObserver((s=>{for(const a of s)if("childList"===a.type)for(const s of a.addedNodes)"LINK"===s.tagName&&"modulepreload"===s.rel&&e(s)})).observe(document,{childList:!0,subtree:!0})}function e(s){if(s.ep)return;s.ep=!0;const e=function(s){const e={};return s.integrity&&(e.integrity=s.integrity),s.referrerPolicy&&(e.referrerPolicy=s.referrerPolicy),"use-credentials"===s.crossOrigin?e.credentials="include":"anonymous"===s.crossOrigin?e.credentials="omit":e.credentials="same-origin",e}(s);fetch(s.href,e)}}();const w={},b=function(s,e,a){let r=Promise.resolve();if(e&&e.length>0){document.getElementsByTagName("link");const s=document.querySelector("meta[property=csp-nonce]"),a=(null==s?void 0:s.nonce)||(null==s?void 0:s.getAttribute("nonce"));r=Promise.allSettled(e.map((s=>{if((s=function(s){return"/"+s}(s))in w)return;w[s]=!0;const e=s.endsWith(".css"),r=e?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${s}"]${r}`))return;const l=document.createElement("link");return l.rel=e?"stylesheet":"modulepreload",e||(l.as="script"),l.crossOrigin="",l.href=s,a&&l.setAttribute("nonce",a),document.head.appendChild(l),e?new Promise(((e,a)=>{l.addEventListener("load",e),l.addEventListener("error",(()=>a(new Error(`Unable to preload CSS for ${s}`))))})):void 0})))}function l(s){const e=new Event("vite:preloadError",{cancelable:!0});if(e.payload=s,window.dispatchEvent(e),!e.defaultPrevented)throw s}return r.then((e=>{for(const s of e||[])"rejected"===s.status&&l(s.reason);return s().catch(l)}))};let _=0;const E=new Map,A=s=>{if(E.has(s))return;const e=setTimeout((()=>{E.delete(s),I({type:"REMOVE_TOAST",toastId:s})}),3e3);E.set(s,e)},S=(s,e)=>{switch(e.type){case"ADD_TOAST":return{...s,toasts:[e.toast,...s.toasts].slice(0,1)};case"UPDATE_TOAST":return{...s,toasts:s.toasts.map((s=>s.id===e.toast.id?{...s,...e.toast}:s))};case"DISMISS_TOAST":{const{toastId:a}=e;return a?A(a):s.toasts.forEach((s=>{A(s.id)})),{...s,toasts:s.toasts.map((s=>s.id===a||void 0===a?{...s,open:!1}:s))}}case"REMOVE_TOAST":return void 0===e.toastId?{...s,toasts:[]}:{...s,toasts:s.toasts.filter((s=>s.id!==e.toastId))}}},T=[];let O={toasts:[]};function I(s){O=S(O,s),T.forEach((s=>{s(O)}))}function k({...s}){const e=(_=(_+1)%Number.MAX_SAFE_INTEGER,_.toString()),a=()=>I({type:"DISMISS_TOAST",toastId:e});return I({type:"ADD_TOAST",toast:{...s,id:e,open:!0,onOpenChange:s=>{s||a()}}}),{id:e,dismiss:a,update:s=>I({type:"UPDATE_TOAST",toast:{...s,id:e}})}}const R={success:(s,e)=>k({title:s,description:null==e?void 0:e.description,variant:"default",className:"bg-green-50 border-green-200 text-green-800"}),error:(s,e)=>k({title:s,description:null==e?void 0:e.description,variant:"destructive",className:"bg-red-50 border-red-200 text-red-800"}),info:(s,e)=>k({title:s,description:null==e?void 0:e.description,variant:"default",className:"bg-blue-50 border-blue-200 text-blue-800"}),warning:(s,e)=>k({title:s,description:null==e?void 0:e.description,variant:"default",className:"bg-yellow-50 border-yellow-200 text-yellow-800"})},P=Object.assign(k,R);function D(...s){return f(y(s))}const L=n,z=s.forwardRef((({className:s,...r},l)=>e.jsx(a,{ref:l,className:D("fixed top-4 right-4 z-[100] flex max-h-screen w-full flex-col p-4 md:max-w-[420px]",s),...r})));z.displayName=a.displayName;const V=g("group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),M=s.forwardRef((({className:s,variant:a,...l},c)=>e.jsx(r,{ref:c,className:D(V({variant:a}),s),...l})));M.displayName=r.displayName;s.forwardRef((({className:s,...a},r)=>e.jsx(l,{ref:r,className:D("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium transition-colors hover:bg-secondary focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",s),...a}))).displayName=l.displayName;const q=s.forwardRef((({className:s,...a},r)=>e.jsx(c,{ref:r,className:D("absolute right-1 top-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",s),"toast-close":"",...a,children:e.jsx(t,{className:"h-4 w-4"})})));q.displayName=c.displayName;const C=s.forwardRef((({className:s,...a},r)=>e.jsx(d,{ref:r,className:D("text-sm font-semibold [&+div]:text-xs",s),...a})));C.displayName=d.displayName;const B=s.forwardRef((({className:s,...a},r)=>e.jsx(i,{ref:r,className:D("text-sm opacity-90",s),...a})));function U(){const{toasts:a}=function(){const[e,a]=s.useState(O);return s.useEffect((()=>(T.push(a),()=>{const s=T.indexOf(a);s>-1&&T.splice(s,1)})),[e]),{...e,toast:k,dismiss:s=>I({type:"DISMISS_TOAST",toastId:s})}}();return e.jsxs(L,{children:[a.map((function({id:s,title:a,description:r,action:l,...c}){return e.jsxs(M,{...c,children:[e.jsxs("div",{className:"grid gap-1",children:[a&&e.jsx(C,{children:a}),r&&e.jsx(B,{children:r})]}),l,e.jsx(q,{})]},s)})),e.jsx(z,{})]})}B.displayName=i.displayName;const $=m;function K({className:s,...a}){return e.jsx("div",{className:D("animate-pulse rounded-md bg-primary/10",s),...a})}s.forwardRef((({className:s,sideOffset:a=4,...r},l)=>e.jsx(o,{ref:l,sideOffset:a,className:D("z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...r}))).displayName=o.displayName;const Q=({type:s="dashboard",count:a=1})=>e.jsx("div",{className:"animate-pulse",children:(()=>{switch(s){case"dashboard":return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"h-8 w-48"}),e.jsx(K,{className:"h-4 w-96"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[...Array(4)].map(((s,a)=>e.jsx("div",{className:"p-6 border rounded-lg space-y-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"h-4 w-24"}),e.jsx(K,{className:"h-8 w-16"}),e.jsx(K,{className:"h-4 w-20"})]}),e.jsx(K,{className:"h-12 w-12 rounded-xl"})]})},a)))}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsx("div",{className:"lg:col-span-2 space-y-6",children:e.jsxs("div",{className:"p-6 border rounded-lg space-y-4",children:[e.jsx(K,{className:"h-6 w-32"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[...Array(4)].map(((s,a)=>e.jsx("div",{className:"p-4 border rounded-lg space-y-3",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(K,{className:"h-10 w-10 rounded-lg"}),e.jsxs("div",{className:"space-y-2 flex-1",children:[e.jsx(K,{className:"h-4 w-24"}),e.jsx(K,{className:"h-3 w-32"})]})]})},a)))})]})}),e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"p-6 border rounded-lg space-y-4",children:[e.jsx(K,{className:"h-6 w-28"}),e.jsx("div",{className:"space-y-3",children:[...Array(4)].map(((s,a)=>e.jsxs("div",{className:"flex items-center space-x-3 p-3 border rounded-lg",children:[e.jsx(K,{className:"h-8 w-8 rounded-full"}),e.jsxs("div",{className:"flex-1 space-y-2",children:[e.jsx(K,{className:"h-4 w-24"}),e.jsx(K,{className:"h-3 w-16"})]})]},a)))})]})})]})]});case"card":return e.jsx("div",{className:"p-6 border rounded-lg space-y-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"h-4 w-24"}),e.jsx(K,{className:"h-8 w-16"})]}),e.jsx(K,{className:"h-12 w-12 rounded-xl"})]})});case"table":return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(K,{className:"h-6 w-32"}),e.jsx(K,{className:"h-9 w-24"})]}),e.jsxs("div",{className:"border rounded-lg",children:[e.jsx("div",{className:"grid grid-cols-4 gap-4 p-4 border-b",children:[...Array(4)].map(((s,a)=>e.jsx(K,{className:"h-4 w-20"},a)))}),[...Array(5)].map(((s,a)=>e.jsx("div",{className:"grid grid-cols-4 gap-4 p-4 border-b last:border-b-0",children:[...Array(4)].map(((s,a)=>e.jsx(K,{className:"h-4 w-16"},a)))},a)))]})]});case"form":return e.jsxs("div",{className:"space-y-4",children:[e.jsx(K,{className:"h-6 w-32"}),[...Array(a)].map(((s,a)=>e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"h-4 w-20"}),e.jsx(K,{className:"h-10 w-full"})]},a))),e.jsx(K,{className:"h-10 w-24"})]});case"profile":return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"h-8 w-48"}),e.jsx(K,{className:"h-4 w-96"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsx("div",{className:"lg:col-span-2",children:e.jsxs("div",{className:"p-6 border rounded-lg space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"h-6 w-32"}),e.jsx(K,{className:"h-4 w-48"})]}),e.jsx(K,{className:"h-10 w-24"})]}),e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsx(K,{className:"h-24 w-24 rounded-full"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"h-6 w-32"}),e.jsx(K,{className:"h-4 w-48"}),e.jsx(K,{className:"h-6 w-16"})]})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[...Array(6)].map(((s,a)=>e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"h-4 w-20"}),e.jsx(K,{className:"h-10 w-full"})]},a)))})]})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"p-6 border rounded-lg space-y-4",children:[e.jsx(K,{className:"h-6 w-28"}),e.jsx("div",{className:"space-y-4",children:[...Array(3)].map(((s,a)=>e.jsx("div",{className:"p-3 border rounded-lg space-y-2",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx(K,{className:"h-4 w-20"}),e.jsx(K,{className:"h-6 w-16"})]}),e.jsx(K,{className:"h-8 w-8"})]})},a)))})]}),e.jsxs("div",{className:"p-6 border rounded-lg space-y-4",children:[e.jsx(K,{className:"h-6 w-24"}),e.jsx("div",{className:"space-y-3",children:[...Array(3)].map(((s,a)=>e.jsx(K,{className:"h-10 w-full"},a)))})]})]})]})]});case"tukar-poin":return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"h-8 w-48"}),e.jsx(K,{className:"h-4 w-96"})]}),e.jsxs("div",{className:"flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit",children:[e.jsx(K,{className:"h-8 w-24"}),e.jsx(K,{className:"h-8 w-32"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map(((s,a)=>e.jsxs("div",{className:"p-6 border rounded-lg space-y-4",children:[e.jsx(K,{className:"h-32 w-full rounded-lg"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"h-5 w-32"}),e.jsx(K,{className:"h-4 w-24"}),e.jsx(K,{className:"h-4 w-20"})]}),e.jsx(K,{className:"h-10 w-full"})]},a)))})]});case"riwayat":return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"h-8 w-48"}),e.jsx(K,{className:"h-4 w-96"})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[e.jsx(K,{className:"h-10 w-48"}),e.jsx(K,{className:"h-10 w-32"}),e.jsx(K,{className:"h-10 w-24"})]}),e.jsxs("div",{className:"border rounded-lg",children:[e.jsx("div",{className:"grid grid-cols-5 gap-4 p-4 border-b",children:[...Array(5)].map(((s,a)=>e.jsx(K,{className:"h-4 w-20"},a)))}),[...Array(8)].map(((s,a)=>e.jsx("div",{className:"grid grid-cols-5 gap-4 p-4 border-b last:border-b-0",children:[...Array(5)].map(((s,a)=>e.jsx(K,{className:"h-4 w-16"},a)))},a)))]})]});case"request-jemput":return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"h-8 w-64"}),e.jsx(K,{className:"h-4 w-96"})]}),e.jsxs("div",{className:"flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit",children:[e.jsx(K,{className:"h-8 w-32"}),e.jsx(K,{className:"h-8 w-24"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsxs("div",{className:"p-6 border rounded-lg space-y-6",children:[e.jsx("div",{className:"space-y-2",children:e.jsx(K,{className:"h-6 w-48"})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"h-4 w-32"}),e.jsx(K,{className:"h-10 w-full"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"h-4 w-32"}),e.jsx(K,{className:"h-10 w-full"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"h-4 w-32"}),e.jsx(K,{className:"h-20 w-full"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"h-4 w-24"}),e.jsx(K,{className:"h-10 w-full"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"h-4 w-20"}),e.jsx(K,{className:"h-20 w-full"})]})]}),e.jsxs("div",{className:"p-6 border rounded-lg space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(K,{className:"h-6 w-32"}),e.jsx(K,{className:"h-8 w-24"})]}),e.jsxs("div",{className:"p-4 border rounded-lg space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"h-4 w-20"}),e.jsx(K,{className:"h-10 w-full"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"h-4 w-16"}),e.jsx(K,{className:"h-10 w-full"})]}),e.jsx("div",{className:"flex items-end",children:e.jsx(K,{className:"h-8 w-8"})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"h-4 w-20"}),e.jsx(K,{className:"h-16 w-full"})]})]})]})]}),e.jsx("div",{children:e.jsxs("div",{className:"p-6 border rounded-lg space-y-4",children:[e.jsx(K,{className:"h-6 w-32"}),e.jsx("div",{className:"space-y-3",children:[...Array(4)].map(((s,a)=>e.jsxs("div",{className:"flex justify-between",children:[e.jsx(K,{className:"h-4 w-16"}),e.jsx(K,{className:"h-4 w-20"})]},a)))}),e.jsx("div",{className:"pt-4 border-t",children:e.jsx(K,{className:"h-10 w-full"})}),e.jsxs("div",{className:"p-3 bg-blue-50 rounded-lg",children:[e.jsx(K,{className:"h-3 w-full mb-2"}),e.jsx(K,{className:"h-3 w-3/4"})]})]})})]})]});default:return e.jsx(K,{className:"h-20 w-full"})}})()}),F=()=>{const a=x();return s.useEffect((()=>{}),[a.pathname]),e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-4xl font-bold mb-4",children:"404"}),e.jsx("p",{className:"text-xl text-gray-600 mb-4",children:"Oops! Page not found"}),e.jsx("a",{href:"/",className:"text-blue-500 hover:text-blue-700 underline",children:"Return to Home"})]})})},G=s.lazy((()=>b((()=>import("./Index-C8OZQZiF.js")),__vite__mapDeps([0,1,2,3,4,5,6,7])))),H=s.lazy((()=>b((()=>import("./Login-CA_6-jVY.js")),__vite__mapDeps([8,1,2,3,4,5,9,10,11,7])))),J=s.lazy((()=>b((()=>import("./Register-M7B8VNnV.js")),__vite__mapDeps([12,1,2,3,4,5,9,10,11,7])))),W=s.lazy((()=>b((()=>import("./Dashboard-CbCztPv-.js")),__vite__mapDeps([13,1,2,3,4,5,6,14,7])))),X=s.lazy((()=>b((()=>import("./Nasabah-CR4li4jx.js")),__vite__mapDeps([15,1,2,3,4,5,9,10,16,17,14,7])))),Y=s.lazy((()=>b((()=>import("./Kategori-CwuodaWH.js")),__vite__mapDeps([18,1,2,3,4,5,9,10,16,17,14,7])))),Z=s.lazy((()=>b((()=>import("./PenjemputanSampah-B36w3vMN.js")),__vite__mapDeps([19,1,2,3,4,5,9,10,16,17,14,7])))),ss=s.lazy((()=>b((()=>import("./Transaksi-DNSD5_6w.js")),__vite__mapDeps([20,1,2,3,4,5,9,10,16,17,6,14,7])))),es=s.lazy((()=>b((()=>import("./Dashboard-C_5S1YLn.js")),__vite__mapDeps([21,1,2,3,4,5,6,22,7])))),as=s.lazy((()=>b((()=>import("./Profil-l0PBmkQI.js")),__vite__mapDeps([23,1,2,3,4,5,9,10,24,6,22,17,7])))),rs=s.lazy((()=>b((()=>import("./RiwayatTransaksi-D5L7d9EQ.js")),__vite__mapDeps([25,1,2,3,22,4,5,6,9,7])))),ls=s.lazy((()=>b((()=>import("./RequestJemput-Cr4l5n-6.js")),__vite__mapDeps([26,1,2,3,22,4,5,6,9,10,24,17,7])))),cs=s.lazy((()=>b((()=>import("./TukarPoin-DDZKeJfs.js")),__vite__mapDeps([27,1,2,3,22,4,5,6,9,17,7])))),ts=new v,ds=({type:s="dashboard"})=>e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsx(Q,{type:s})}),is=()=>e.jsx(p,{client:ts,children:e.jsxs($,{children:[e.jsx(U,{}),e.jsx(j,{children:e.jsx(s.Suspense,{fallback:e.jsx(ds,{}),children:e.jsxs(h,{children:[e.jsx(u,{path:"/",element:e.jsx(G,{})}),e.jsx(u,{path:"/login",element:e.jsx(s.Suspense,{fallback:e.jsx(ds,{type:"form"}),children:e.jsx(H,{})})}),e.jsx(u,{path:"/register",element:e.jsx(s.Suspense,{fallback:e.jsx(ds,{type:"form"}),children:e.jsx(J,{})})}),e.jsx(u,{path:"/admin/dashboard",element:e.jsx(s.Suspense,{fallback:e.jsx(ds,{type:"dashboard"}),children:e.jsx(W,{})})}),e.jsx(u,{path:"/admin/nasabah",element:e.jsx(s.Suspense,{fallback:e.jsx(ds,{type:"table"}),children:e.jsx(X,{})})}),e.jsx(u,{path:"/admin/kategori",element:e.jsx(s.Suspense,{fallback:e.jsx(ds,{type:"table"}),children:e.jsx(Y,{})})}),e.jsx(u,{path:"/admin/penjemputan",element:e.jsx(s.Suspense,{fallback:e.jsx(ds,{type:"table"}),children:e.jsx(Z,{})})}),e.jsx(u,{path:"/admin/transaksi",element:e.jsx(s.Suspense,{fallback:e.jsx(ds,{type:"table"}),children:e.jsx(ss,{})})}),e.jsx(u,{path:"/nasabah/dashboard",element:e.jsx(s.Suspense,{fallback:e.jsx(ds,{type:"dashboard"}),children:e.jsx(es,{})})}),e.jsx(u,{path:"/nasabah/profil",element:e.jsx(s.Suspense,{fallback:e.jsx(ds,{type:"profile"}),children:e.jsx(as,{})})}),e.jsx(u,{path:"/nasabah/riwayat",element:e.jsx(s.Suspense,{fallback:e.jsx(ds,{type:"riwayat"}),children:e.jsx(rs,{})})}),e.jsx(u,{path:"/nasabah/jemput",element:e.jsx(s.Suspense,{fallback:e.jsx(ds,{type:"request-jemput"}),children:e.jsx(ls,{})})}),e.jsx(u,{path:"/nasabah/tukar-poin",element:e.jsx(s.Suspense,{fallback:e.jsx(ds,{type:"tukar-poin"}),children:e.jsx(cs,{})})}),e.jsx(u,{path:"*",element:e.jsx(F,{})})]})})})]})});N(document.getElementById("root")).render(e.jsx(is,{}));export{Q as S,D as c,P as e};
