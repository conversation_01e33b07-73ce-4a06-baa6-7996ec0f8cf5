import{f as e,r as a,j as s,v as t,h as r,I as i,F as n,J as l,G as d,H as c}from"./react-vendor-C9kdeAq4.js";import{C as m,c as o,a as x,b as h,d as g,B as u}from"./card-l7WlC16I.js";import{I as j}from"./input-B25hGi49.js";import{L as p}from"./label-ZSTvXwUz.js";import{T as b,a as f,b as N,c as k,d as y,e as v}from"./table-qPaxNgV0.js";import{D as w,a as K,b as D,c as S,d as C,e as T,C as A}from"./ConfirmDialog-KlxVkBeS.js";import{e as I,S as M}from"./index-BZfOZLOv.js";import{A as _}from"./AdminSidebar-y32hu_7f.js";import"./radix-vendor-CttiZxwU.js";import"./vendor-C0DTsUaw.js";import"./utils-vendor-DeZn2tlB.js";import"./query-vendor-B0Wv6VB8.js";const B=()=>{const B=e(),[H,E]=a.useState([]),[F,O]=a.useState(!0),[$,q]=a.useState(null),[L,J]=a.useState(null),[P,Y]=a.useState({name:""}),[G,z]=a.useState(!1),[Q,R]=a.useState("add"),U=[{id:1,name:"Plastik",created_at:"2024-01-01"},{id:2,name:"Kertas",created_at:"2024-01-02"},{id:3,name:"Logam",created_at:"2024-01-03"},{id:4,name:"Kaca",created_at:"2024-01-04"}];a.useEffect((()=>{const e=localStorage.getItem("user");if(!e)return void B("/login");if("admin"!==JSON.parse(e).role)return I.error("Akses Ditolak",{description:"Anda tidak memiliki akses ke halaman admin"}),void B("/");setTimeout((()=>{E(U),O(!1)}),1e3)}),[B]);if(F)return s.jsxs("div",{className:"min-h-screen bg-gray-50 flex",children:[s.jsx(_,{}),s.jsx("div",{className:"flex-1 lg:ml-0 p-4 lg:p-8 pt-16 lg:pt-8",children:s.jsx(M,{type:"table"})})]});const V={total:H.length,recentlyAdded:H.filter((e=>{const a=new Date(e.created_at),s=new Date;return s.setDate(s.getDate()-7),a>=s})).length,thisMonth:H.filter((e=>{const a=new Date(e.created_at),s=new Date;return a.getMonth()===s.getMonth()&&a.getFullYear()===s.getFullYear()})).length};return s.jsxs("div",{className:"min-h-screen bg-gray-50 flex",children:[s.jsx(_,{}),s.jsx("div",{className:"flex-1 lg:ml-0",children:s.jsxs("main",{className:"p-4 lg:p-8",children:[s.jsxs("div",{className:"mb-8",children:[s.jsxs("h2",{className:"text-2xl font-bold text-gray-800 mb-2 flex items-center",children:[s.jsx(t,{className:"w-6 h-6 mr-3 text-bank-green-600"}),"Kelola Kategori"]}),s.jsx("p",{className:"text-gray-600",children:"Kelola kategori sampah untuk sistem Bank Sampah Digital"})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[s.jsx(m,{className:"hover:shadow-lg transition-shadow duration-300",children:s.jsx(o,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Total Kategori"}),s.jsx("p",{className:"text-2xl font-bold text-gray-900",children:V.total})]}),s.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-bank-green-100 to-bank-green-200 rounded-xl flex items-center justify-center",children:s.jsx(t,{className:"w-6 h-6 text-bank-green-600"})})]})})}),s.jsx(m,{className:"hover:shadow-lg transition-shadow duration-300",children:s.jsx(o,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Baru Minggu Ini"}),s.jsx("p",{className:"text-2xl font-bold text-blue-600",children:V.recentlyAdded})]}),s.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-100 to-blue-200 rounded-xl flex items-center justify-center",children:s.jsx(r,{className:"w-6 h-6 text-blue-600"})})]})})}),s.jsx(m,{className:"hover:shadow-lg transition-shadow duration-300",children:s.jsx(o,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Bulan Ini"}),s.jsx("p",{className:"text-2xl font-bold text-purple-600",children:V.thisMonth})]}),s.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-purple-100 to-purple-200 rounded-xl flex items-center justify-center",children:s.jsx(i,{className:"w-6 h-6 text-purple-600"})})]})})})]}),s.jsxs(m,{className:"shadow-xl border-0 bg-gradient-to-br from-white to-gray-50",children:[s.jsx(x,{className:"bg-gradient-to-r from-bank-green-50 to-bank-blue-50 rounded-t-lg border-b border-gray-100",children:s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsxs("div",{children:[s.jsxs(h,{className:"text-2xl font-bold text-gray-800 flex items-center gap-3",children:[s.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-bank-green-500 to-bank-green-600 rounded-xl flex items-center justify-center",children:s.jsx(t,{className:"w-5 h-5 text-white"})}),"Daftar Kategori"]}),s.jsx(g,{className:"text-gray-600 mt-2",children:"Kelola kategori sampah yang dapat diterima sistem"})]}),s.jsxs("button",{onClick:()=>{R("add"),Y({name:""}),z(!0)},className:"btn-add group",children:[s.jsx(n,{className:"w-5 h-5 group-hover:rotate-90 transition-transform duration-300"}),"Tambah Kategori"]})]})}),s.jsx(o,{className:"p-0",children:s.jsxs(b,{children:[s.jsx(f,{children:s.jsxs(N,{className:"bg-gray-50/50",children:[s.jsx(k,{className:"font-semibold text-gray-700",children:"ID"}),s.jsx(k,{className:"font-semibold text-gray-700",children:"Nama Kategori"}),s.jsx(k,{className:"font-semibold text-gray-700",children:"Tanggal Dibuat"}),s.jsx(k,{className:"font-semibold text-gray-700",children:"Aksi"})]})}),s.jsx(y,{children:H.map((e=>s.jsxs(N,{className:"hover:bg-gray-50/50 transition-colors",children:[s.jsx(v,{className:"font-semibold text-gray-700",children:e.id}),s.jsx(v,{className:"font-medium text-gray-800",children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-bank-green-100 to-bank-green-200 rounded-lg flex items-center justify-center",children:s.jsx(l,{className:"w-4 h-4 text-bank-green-600"})}),e.name]})}),s.jsx(v,{className:"text-gray-600",children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(i,{className:"w-4 h-4 text-gray-400"}),new Date(e.created_at).toLocaleDateString("id-ID")]})}),s.jsx(v,{children:s.jsxs("div",{className:"flex space-x-2",children:[s.jsxs("button",{className:"btn-edit group flex items-center gap-2 px-3 py-2 text-sm rounded-lg",onClick:()=>(e=>{R("edit"),q(e),Y({name:e.name}),z(!0)})(e),title:"Edit Kategori",children:[s.jsx(d,{className:"w-4 h-4 group-hover:scale-110 transition-transform duration-200"}),"Edit"]}),s.jsxs("button",{className:"btn-delete group flex items-center gap-2 px-3 py-2 text-sm rounded-lg",onClick:()=>(e=>{J(e)})(e),title:"Hapus Kategori",children:[s.jsx(c,{className:"w-4 h-4 group-hover:scale-110 transition-transform duration-200"}),"Hapus"]})]})})]},e.id)))})]})})]})]})}),s.jsx(w,{open:G,onOpenChange:z,children:s.jsxs(K,{children:[s.jsxs(D,{children:[s.jsx(S,{children:"add"===Q?"Tambah Kategori Baru":"Edit Kategori"}),s.jsx(C,{children:"add"===Q?"Masukkan nama kategori sampah baru":"Perbarui nama kategori sampah"})]}),s.jsxs("form",{onSubmit:e=>{if(e.preventDefault(),"add"===Q){const e={id:Math.max(...H.map((e=>e.id)))+1,name:P.name,created_at:(new Date).toISOString().split("T")[0]};E((a=>[...a,e])),I.success("Kategori baru berhasil ditambahkan!",{description:`Kategori "${P.name}" telah ditambahkan`})}else if($){const e={...$,name:P.name};E((a=>a.map((a=>a.id===$.id?e:a)))),I.success("Kategori berhasil diperbarui!",{description:`Kategori "${P.name}" telah diperbaharui`})}z(!1),q(null)},className:"space-y-4",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsx(p,{htmlFor:"name",children:"Nama Kategori"}),s.jsx(j,{id:"name",name:"name",value:P.name,onChange:e=>Y({name:e.target.value}),placeholder:"Masukkan nama kategori",required:!0})]}),s.jsxs(T,{children:[s.jsx(u,{type:"button",variant:"outline",onClick:()=>z(!1),children:"Batal"}),s.jsx(u,{type:"submit",className:"btn-primary",children:"add"===Q?"Tambah":"Simpan"})]})]})]})}),s.jsx(A,{isOpen:!!L,onClose:()=>J(null),onConfirm:()=>{L&&(E((e=>e.filter((e=>e.id!==L.id)))),I.success("Kategori berhasil dihapus!",{description:`Kategori "${L.name}" telah dihapus dari sistem`}),J(null))},title:"Hapus Kategori",description:`Apakah Anda yakin ingin menghapus kategori "${null==L?void 0:L.name}"? Semua data sampah yang terkait dengan kategori ini akan ikut terhapus. Tindakan ini tidak dapat dibatalkan.`,confirmText:"Hapus",cancelText:"Batal",type:"danger"})]})};export{B as default};
