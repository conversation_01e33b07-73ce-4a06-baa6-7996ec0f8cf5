import{K as e,r as a,N as s,j as t,O as r,W as n,Y as i,Z as l,_ as d,$ as m,a0 as c,a1 as o,s as x,a2 as h,a3 as u,a4 as p,a5 as j,a6 as g,a7 as f,f as N,w as b,a8 as w,a9 as v,aa as y,F as _,i as k,I as S,H as q}from"./react-vendor-C9kdeAq4.js";import{C as D,c as P,a as C,b as T,d as J,B as A}from"./card-l7WlC16I.js";import{I as B}from"./input-B25hGi49.js";import{L as F}from"./label-ZSTvXwUz.js";import{c as K,S as R,e as H}from"./index-BZfOZLOv.js";import{T as I,a as M,b as O,c as $,d as z,e as W}from"./table-qPaxNgV0.js";import{D as Z,a as E,b as L,c as V,d as Y,e as G,C as Q}from"./ConfirmDialog-KlxVkBeS.js";import{A as U}from"./AdminSidebar-y32hu_7f.js";import"./radix-vendor-CttiZxwU.js";import"./vendor-C0DTsUaw.js";import"./utils-vendor-DeZn2tlB.js";import"./query-vendor-B0Wv6VB8.js";const X=e,ee=s,ae=a.forwardRef((({className:e,children:a,...s},l)=>t.jsxs(r,{ref:l,className:K("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[a,t.jsx(n,{asChild:!0,children:t.jsx(i,{className:"h-4 w-4 opacity-50"})})]})));ae.displayName=r.displayName;const se=a.forwardRef((({className:e,...a},s)=>t.jsx(u,{ref:s,className:K("flex cursor-default items-center justify-center py-1",e),...a,children:t.jsx(p,{className:"h-4 w-4"})})));se.displayName=u.displayName;const te=a.forwardRef((({className:e,...a},s)=>t.jsx(j,{ref:s,className:K("flex cursor-default items-center justify-center py-1",e),...a,children:t.jsx(i,{className:"h-4 w-4"})})));te.displayName=j.displayName;const re=a.forwardRef((({className:e,children:a,position:s="popper",...r},n)=>t.jsx(l,{children:t.jsxs(d,{ref:n,className:K("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...r,children:[t.jsx(se,{}),t.jsx(m,{className:K("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),t.jsx(te,{})]})})));re.displayName=d.displayName;a.forwardRef((({className:e,...a},s)=>t.jsx(g,{ref:s,className:K("px-2 py-1.5 text-sm font-semibold",e),...a}))).displayName=g.displayName;const ne=a.forwardRef((({className:e,children:a,...s},r)=>t.jsxs(c,{ref:r,className:K("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[t.jsx("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:t.jsx(o,{children:t.jsx(x,{className:"h-4 w-4"})})}),t.jsx(h,{children:a})]})));ne.displayName=c.displayName;a.forwardRef((({className:e,...a},s)=>t.jsx(f,{ref:s,className:K("-mx-1 my-1 h-px bg-muted",e),...a}))).displayName=f.displayName;const ie=()=>{const e=N(),[s,r]=a.useState(null),[n,i]=a.useState(!0),[l,d]=a.useState([]),[m,c]=a.useState(!1),[o,x]=a.useState(null),[h,u]=a.useState({user_name:"",waste_name:"",address:"",date_request:"",estimated_weight:"",status:"pending"}),p=[{id:"1",user_id:"1",user_name:"Siti Nurhaliza",waste_id:"1",waste_name:"Plastik Botol",address:"Jl. Merdeka No. 123, Jakarta Pusat",date_request:"2024-01-20",estimated_weight:5.5,status:"pending",created_at:"2024-01-15T10:00:00Z"},{id:"2",user_id:"2",user_name:"Budi Santoso",waste_id:"2",waste_name:"Kertas Bekas",address:"Jl. Sudirman No. 456, Jakarta Selatan",date_request:"2024-01-22",estimated_weight:8.2,status:"approved",created_at:"2024-01-16T14:30:00Z"},{id:"3",user_id:"3",user_name:"Ahmad Wijaya",waste_id:"3",waste_name:"Logam Bekas",address:"Jl. Thamrin No. 789, Jakarta Pusat",date_request:"2024-01-18",estimated_weight:12,status:"completed",created_at:"2024-01-12T09:15:00Z"}];a.useEffect((()=>{(async()=>{i(!0),await new Promise((e=>setTimeout(e,1500)));const a=localStorage.getItem("user");if(!a)return void e("/login");const s=JSON.parse(a);if("admin"!==s.role)return H.error("Akses Ditolak",{description:"Anda tidak memiliki akses ke halaman admin"}),void e("/");r(s),d(p),i(!1)})()}),[e]);const j={total:l.length,pending:l.filter((e=>"pending"===e.status)).length,approved:l.filter((e=>"approved"===e.status)).length,completed:l.filter((e=>"completed"===e.status)).length,totalWeight:l.reduce(((e,a)=>e+a.estimated_weight),0)};return n?t.jsxs("div",{className:"min-h-screen bg-gray-50 flex",children:[t.jsx(U,{}),t.jsx("div",{className:"flex-1 lg:ml-0 p-4 lg:p-8 pt-16 lg:pt-8",children:t.jsx(R,{type:"table"})})]}):s?t.jsxs("div",{className:"min-h-screen bg-gray-50 flex",children:[t.jsx(U,{}),t.jsx("div",{className:"flex-1 lg:ml-0",children:t.jsxs("main",{className:"p-4 lg:p-8",children:[t.jsxs("div",{className:"mb-8",children:[t.jsxs("h2",{className:"text-2xl font-bold text-gray-800 mb-2 flex items-center",children:[t.jsx(b,{className:"w-6 h-6 mr-3 text-bank-green-600"}),"Kelola Penjemputan Sampah"]}),t.jsx("p",{className:"text-gray-600",children:"Kelola permintaan penjemputan sampah dari nasabah"})]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8",children:[t.jsx(D,{className:"hover:shadow-lg transition-shadow duration-300",children:t.jsx(P,{className:"p-6",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{children:[t.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Total Permintaan"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900",children:j.total})]}),t.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-bank-green-100 to-bank-green-200 rounded-xl flex items-center justify-center",children:t.jsx(b,{className:"w-6 h-6 text-bank-green-600"})})]})})}),t.jsx(D,{className:"hover:shadow-lg transition-shadow duration-300",children:t.jsx(P,{className:"p-6",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{children:[t.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Menunggu"}),t.jsx("p",{className:"text-2xl font-bold text-yellow-600",children:j.pending})]}),t.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-yellow-100 to-yellow-200 rounded-xl flex items-center justify-center",children:t.jsx(w,{className:"w-6 h-6 text-yellow-600"})})]})})}),t.jsx(D,{className:"hover:shadow-lg transition-shadow duration-300",children:t.jsx(P,{className:"p-6",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{children:[t.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Disetujui"}),t.jsx("p",{className:"text-2xl font-bold text-blue-600",children:j.approved})]}),t.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-100 to-blue-200 rounded-xl flex items-center justify-center",children:t.jsx(v,{className:"w-6 h-6 text-blue-600"})})]})})}),t.jsx(D,{className:"hover:shadow-lg transition-shadow duration-300",children:t.jsx(P,{className:"p-6",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{children:[t.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Selesai"}),t.jsx("p",{className:"text-2xl font-bold text-bank-green-600",children:j.completed})]}),t.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-bank-green-100 to-bank-green-200 rounded-xl flex items-center justify-center",children:t.jsx(v,{className:"w-6 h-6 text-bank-green-600"})})]})})}),t.jsx(D,{className:"hover:shadow-lg transition-shadow duration-300",children:t.jsx(P,{className:"p-6",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{children:[t.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Total Berat"}),t.jsxs("p",{className:"text-2xl font-bold text-purple-600",children:[j.totalWeight.toFixed(1)," Kg"]})]}),t.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-purple-100 to-purple-200 rounded-xl flex items-center justify-center",children:t.jsx(y,{className:"w-6 h-6 text-purple-600"})})]})})})]}),t.jsxs(D,{className:"shadow-xl border-0 bg-gradient-to-br from-white to-gray-50",children:[t.jsx(C,{className:"bg-gradient-to-r from-bank-green-50 to-bank-blue-50 rounded-t-lg border-b border-gray-100",children:t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsxs("div",{children:[t.jsxs(T,{className:"text-2xl font-bold text-gray-800 flex items-center gap-3",children:[t.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-bank-green-500 to-bank-green-600 rounded-xl flex items-center justify-center",children:t.jsx(b,{className:"w-5 h-5 text-white"})}),"Daftar Permintaan Penjemputan"]}),t.jsx(J,{className:"text-gray-600 mt-2",children:"Kelola permintaan penjemputan sampah dari nasabah"})]}),t.jsxs("button",{onClick:()=>{u({user_name:"",waste_name:"",address:"",date_request:"",estimated_weight:"",status:"pending"}),c(!0)},className:"btn-add group",children:[t.jsx(_,{className:"w-5 h-5 group-hover:rotate-90 transition-transform duration-300"}),"Tambah Permintaan"]})]})}),t.jsx(P,{className:"p-0",children:t.jsxs(I,{children:[t.jsx(M,{children:t.jsxs(O,{className:"bg-gray-50/50",children:[t.jsx($,{className:"font-semibold text-gray-700",children:"Nasabah"}),t.jsx($,{className:"font-semibold text-gray-700",children:"Jenis Sampah"}),t.jsx($,{className:"font-semibold text-gray-700",children:"Alamat"}),t.jsx($,{className:"font-semibold text-gray-700",children:"Tanggal Jemput"}),t.jsx($,{className:"font-semibold text-gray-700",children:"Estimasi Berat"}),t.jsx($,{className:"font-semibold text-gray-700",children:"Status"}),t.jsx($,{className:"font-semibold text-gray-700",children:"Aksi"})]})}),t.jsx(z,{children:l.map((e=>t.jsxs(O,{className:"hover:bg-gray-50/50 transition-colors",children:[t.jsx(W,{className:"font-medium text-gray-800",children:e.user_name}),t.jsx(W,{className:"text-gray-600",children:e.waste_name}),t.jsx(W,{className:"text-gray-600 max-w-xs truncate",title:e.address,children:t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(k,{className:"w-4 h-4 text-gray-400"}),e.address]})}),t.jsx(W,{className:"text-gray-600",children:t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(S,{className:"w-4 h-4 text-gray-400"}),new Date(e.date_request).toLocaleDateString("id-ID")]})}),t.jsx(W,{className:"text-gray-600",children:t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(y,{className:"w-4 h-4 text-gray-400"}),e.estimated_weight," Kg"]})}),t.jsx(W,{children:t.jsxs(X,{value:e.status,onValueChange:a=>((e,a)=>{d((s=>s.map((s=>s.id===e?{...s,status:a}:s))));const s={pending:"Menunggu",approved:"Disetujui",completed:"Selesai",cancelled:"Dibatalkan"}[a]||a;H.success("Status berhasil diperbarui!",{description:`Status permintaan diubah menjadi ${s}`})})(e.id,a),children:[t.jsx(ae,{className:"w-32 h-8 text-xs",children:t.jsx(ee,{})}),t.jsxs(re,{children:[t.jsx(ne,{value:"pending",children:t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),"Menunggu"]})}),t.jsx(ne,{value:"approved",children:t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"Disetujui"]})}),t.jsx(ne,{value:"completed",children:t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"Selesai"]})}),t.jsx(ne,{value:"cancelled",children:t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),"Dibatalkan"]})})]})]})}),t.jsx(W,{children:t.jsxs("button",{className:"btn-delete group flex items-center gap-2 px-3 py-2 text-sm rounded-lg",onClick:()=>(e=>{x(e)})(e),title:"Hapus Permintaan",children:[t.jsx(q,{className:"w-4 h-4 group-hover:scale-110 transition-transform duration-200"}),"Hapus"]})})]},e.id)))})]})})]})]})}),t.jsx(Z,{open:m,onOpenChange:c,children:t.jsxs(E,{className:"sm:max-w-md",children:[t.jsxs(L,{children:[t.jsxs(V,{className:"flex items-center gap-2",children:[t.jsx(_,{className:"w-5 h-5 text-bank-green-600"}),"Tambah Permintaan Penjemputan"]}),t.jsx(Y,{children:"Tambahkan permintaan penjemputan sampah baru"})]}),t.jsxs("form",{onSubmit:e=>{e.preventDefault();const a={id:Date.now().toString(),user_id:Date.now().toString(),user_name:h.user_name,waste_id:Date.now().toString(),waste_name:h.waste_name,address:h.address,date_request:h.date_request,estimated_weight:parseFloat(h.estimated_weight),status:h.status,created_at:(new Date).toISOString()};d((e=>[...e,a])),H.success("Permintaan penjemputan baru berhasil ditambahkan!",{description:`Permintaan dari ${h.user_name} telah terdaftar`}),c(!1),u({user_name:"",waste_name:"",address:"",date_request:"",estimated_weight:"",status:"pending"})},className:"space-y-4",children:[t.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[t.jsxs("div",{children:[t.jsx(F,{htmlFor:"user_name",children:"Nama Nasabah"}),t.jsx(B,{id:"user_name",value:h.user_name,onChange:e=>u((a=>({...a,user_name:e.target.value}))),required:!0})]}),t.jsxs("div",{children:[t.jsx(F,{htmlFor:"waste_name",children:"Jenis Sampah"}),t.jsx(B,{id:"waste_name",value:h.waste_name,onChange:e=>u((a=>({...a,waste_name:e.target.value}))),required:!0})]})]}),t.jsxs("div",{children:[t.jsx(F,{htmlFor:"address",children:"Alamat"}),t.jsx(B,{id:"address",value:h.address,onChange:e=>u((a=>({...a,address:e.target.value}))),required:!0})]}),t.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[t.jsxs("div",{children:[t.jsx(F,{htmlFor:"date_request",children:"Tanggal Jemput"}),t.jsx(B,{id:"date_request",type:"date",value:h.date_request,onChange:e=>u((a=>({...a,date_request:e.target.value}))),required:!0})]}),t.jsxs("div",{children:[t.jsx(F,{htmlFor:"estimated_weight",children:"Estimasi Berat (Kg)"}),t.jsx(B,{id:"estimated_weight",type:"number",step:"0.1",value:h.estimated_weight,onChange:e=>u((a=>({...a,estimated_weight:e.target.value}))),required:!0})]})]}),t.jsxs("div",{children:[t.jsx(F,{htmlFor:"status",children:"Status"}),t.jsxs(X,{value:h.status,onValueChange:e=>u((a=>({...a,status:e}))),children:[t.jsx(ae,{children:t.jsx(ee,{})}),t.jsxs(re,{children:[t.jsx(ne,{value:"pending",children:"Menunggu"}),t.jsx(ne,{value:"approved",children:"Disetujui"}),t.jsx(ne,{value:"completed",children:"Selesai"}),t.jsx(ne,{value:"cancelled",children:"Dibatalkan"})]})]})]}),t.jsxs(G,{children:[t.jsx(A,{type:"button",variant:"outline",onClick:()=>c(!1),children:"Batal"}),t.jsx(A,{type:"submit",className:"btn-primary",children:"Tambah Permintaan"})]})]})]})}),t.jsx(Q,{isOpen:!!o,onClose:()=>x(null),onConfirm:()=>{o&&(d((e=>e.filter((e=>e.id!==o.id)))),H.success("Permintaan penjemputan berhasil dihapus!",{description:`Permintaan dari ${o.user_name} telah dihapus dari sistem`}),x(null))},title:"Hapus Permintaan Penjemputan",description:`Apakah Anda yakin ingin menghapus permintaan penjemputan dari ${null==o?void 0:o.user_name}? Tindakan ini tidak dapat dibatalkan.`,confirmText:"Hapus",cancelText:"Batal",type:"danger"})]}):null};export{ie as default};
