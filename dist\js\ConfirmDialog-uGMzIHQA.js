import{aA as e,r as t,j as a,aB as o,aC as s,aD as r,X as n,aE as l,aF as i,aG as d,aH as c,ab as m,aI as f,J as x}from"./react-vendor-BH2w6qpU.js";import{c as g}from"./index-BSlVarex.js";import{B as u}from"./card-DfBXoe59.js";const p=e,y=o,b=t.forwardRef((({className:e,...t},o)=>a.jsx(d,{ref:o,className:g("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t})));b.displayName=d.displayName;const h=t.forwardRef((({className:e,children:t,...o},l)=>a.jsxs(y,{children:[a.jsx(b,{}),a.jsxs(s,{ref:l,className:g("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...o,children:[t,a.jsxs(r,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[a.jsx(n,{className:"h-4 w-4"}),a.jsx("span",{className:"sr-only",children:"Close"})]})]})]})));h.displayName=s.displayName;const w=({className:e,...t})=>a.jsx("div",{className:g("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});w.displayName="DialogHeader";const N=({className:e,...t})=>a.jsx("div",{className:g("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});N.displayName="DialogFooter";const j=t.forwardRef((({className:e,...t},o)=>a.jsx(l,{ref:o,className:g("text-lg font-semibold leading-none tracking-tight",e),...t})));j.displayName=l.displayName;const v=t.forwardRef((({className:e,...t},o)=>a.jsx(i,{ref:o,className:g("text-sm text-muted-foreground",e),...t})));v.displayName=i.displayName;const C=({isOpen:e,onClose:t,onConfirm:o,title:s,description:r,confirmText:n="Konfirmasi",cancelText:l="Batal",type:i="danger"})=>{const d=(()=>{switch(i){case"danger":return{icon:x,iconBg:"bg-gradient-to-r from-red-100 to-red-200",iconColor:"text-red-600",confirmButton:"btn-delete",titleColor:"text-red-800"};case"warning":return{icon:c,iconBg:"bg-gradient-to-r from-yellow-100 to-yellow-200",iconColor:"text-yellow-600",confirmButton:"bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white font-semibold py-2.5 px-6 rounded-lg transition-all duration-300 hover:shadow-lg transform hover:-translate-y-0.5",titleColor:"text-yellow-800"};case"info":return{icon:f,iconBg:"bg-gradient-to-r from-blue-100 to-blue-200",iconColor:"text-blue-600",confirmButton:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-2.5 px-6 rounded-lg transition-all duration-300 hover:shadow-lg transform hover:-translate-y-0.5",titleColor:"text-blue-800"};case"success":return{icon:m,iconBg:"bg-gradient-to-r from-bank-green-100 to-bank-green-200",iconColor:"text-bank-green-600",confirmButton:"btn-primary",titleColor:"text-bank-green-800"};default:return{icon:c,iconBg:"bg-gradient-to-r from-gray-100 to-gray-200",iconColor:"text-gray-600",confirmButton:"btn-primary",titleColor:"text-gray-800"}}})(),g=d.icon;return a.jsx(p,{open:e,onOpenChange:t,children:a.jsxs(h,{className:"sm:max-w-md bg-white border-0 shadow-2xl",children:[a.jsxs(w,{className:"text-center space-y-4",children:[a.jsx("div",{className:"mx-auto w-16 h-16 rounded-full flex items-center justify-center",children:a.jsx("div",{className:`w-full h-full ${d.iconBg} rounded-full flex items-center justify-center shadow-lg`,children:a.jsx(g,{className:`w-8 h-8 ${d.iconColor}`})})}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(j,{className:`text-xl font-bold ${d.titleColor}`,children:s}),a.jsx(v,{className:"text-gray-600 text-base leading-relaxed",children:r})]})]}),a.jsxs(N,{className:"flex flex-col sm:flex-row gap-3 pt-6",children:[a.jsx(u,{type:"button",variant:"outline",onClick:t,className:"w-full sm:w-auto order-2 sm:order-1 bg-white hover:bg-gray-50 text-gray-700 border-2 border-gray-300 hover:border-gray-400 font-medium py-2.5 px-6 rounded-lg transition-all duration-300",children:l}),a.jsx(u,{type:"button",onClick:()=>{o(),t()},className:`w-full sm:w-auto order-1 sm:order-2 ${d.confirmButton}`,children:n})]})]})})};export{C,p as D,h as a,w as b,j as c,v as d,N as e};
