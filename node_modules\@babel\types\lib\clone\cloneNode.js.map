{"version": 3, "names": ["_index", "require", "_index2", "hasOwn", "Function", "call", "bind", "Object", "prototype", "hasOwnProperty", "cloneIfNode", "obj", "deep", "withoutLoc", "commentsCache", "type", "cloneNodeInternal", "cloneIfNodeOrArray", "Array", "isArray", "map", "node", "cloneNode", "Map", "newNode", "isIdentifier", "name", "optional", "typeAnnotation", "decorators", "NODE_FIELDS", "Error", "field", "keys", "isFile", "maybeCloneComments", "comments", "loc", "leadingComments", "innerComments", "trailingComments", "extra", "assign", "comment", "cache", "get", "value", "ret", "set"], "sources": ["../../src/clone/cloneNode.ts"], "sourcesContent": ["import { NODE_FIELDS } from \"../definitions/index.ts\";\nimport type * as t from \"../index.ts\";\nimport { isFile, isIdentifier } from \"../validators/generated/index.ts\";\n\nconst { hasOwn } = process.env.BABEL_8_BREAKING\n  ? Object\n  : { hasOwn: Function.call.bind(Object.prototype.hasOwnProperty) };\n\ntype CommentCache = Map<t.Comment, t.Comment>;\n\n// This function will never be called for comments, only for real nodes.\nfunction cloneIfNode(\n  obj: t.Node | undefined | null,\n  deep: boolean,\n  withoutLoc: boolean,\n  commentsCache: CommentCache,\n) {\n  if (obj && typeof obj.type === \"string\") {\n    return cloneNodeInternal(obj, deep, withoutLoc, commentsCache);\n  }\n\n  return obj;\n}\n\nfunction cloneIfNodeOrArray(\n  obj: t.Node | undefined | null | (t.Node | undefined | null)[],\n  deep: boolean,\n  withoutLoc: boolean,\n  commentsCache: CommentCache,\n) {\n  if (Array.isArray(obj)) {\n    return obj.map(node => cloneIfNode(node, deep, withoutLoc, commentsCache));\n  }\n  return cloneIfNode(obj, deep, withoutLoc, commentsCache);\n}\n\n/**\n * Create a clone of a `node` including only properties belonging to the node.\n * If the second parameter is `false`, cloneNode performs a shallow clone.\n * If the third parameter is true, the cloned nodes exclude location properties.\n */\nexport default function cloneNode<T extends t.Node>(\n  node: T,\n  deep: boolean = true,\n  withoutLoc: boolean = false,\n): T {\n  return cloneNodeInternal(node, deep, withoutLoc, new Map());\n}\n\nfunction cloneNodeInternal<T extends t.Node>(\n  node: T,\n  deep: boolean = true,\n  withoutLoc: boolean = false,\n  commentsCache: CommentCache,\n): T {\n  if (!node) return node;\n\n  const { type } = node;\n  const newNode: any = { type: node.type };\n\n  // Special-case identifiers since they are the most cloned nodes.\n  if (isIdentifier(node)) {\n    newNode.name = node.name;\n\n    if (hasOwn(node, \"optional\") && typeof node.optional === \"boolean\") {\n      newNode.optional = node.optional;\n    }\n\n    if (hasOwn(node, \"typeAnnotation\")) {\n      newNode.typeAnnotation = deep\n        ? cloneIfNodeOrArray(\n            node.typeAnnotation,\n            true,\n            withoutLoc,\n            commentsCache,\n          )\n        : node.typeAnnotation;\n    }\n\n    if (hasOwn(node, \"decorators\")) {\n      newNode.decorators = deep\n        ? cloneIfNodeOrArray(node.decorators, true, withoutLoc, commentsCache)\n        : node.decorators;\n    }\n  } else if (!hasOwn(NODE_FIELDS, type)) {\n    throw new Error(`Unknown node type: \"${type}\"`);\n  } else {\n    for (const field of Object.keys(NODE_FIELDS[type])) {\n      if (hasOwn(node, field)) {\n        if (deep) {\n          newNode[field] =\n            isFile(node) && field === \"comments\"\n              ? maybeCloneComments(\n                  node.comments,\n                  deep,\n                  withoutLoc,\n                  commentsCache,\n                )\n              : cloneIfNodeOrArray(\n                  // @ts-expect-error node[field] has been guarded by has check\n                  node[field],\n                  true,\n                  withoutLoc,\n                  commentsCache,\n                );\n        } else {\n          newNode[field] =\n            // @ts-expect-error node[field] has been guarded by has check\n            node[field];\n        }\n      }\n    }\n  }\n\n  if (hasOwn(node, \"loc\")) {\n    if (withoutLoc) {\n      newNode.loc = null;\n    } else {\n      newNode.loc = node.loc;\n    }\n  }\n  if (hasOwn(node, \"leadingComments\")) {\n    newNode.leadingComments = maybeCloneComments(\n      node.leadingComments,\n      deep,\n      withoutLoc,\n      commentsCache,\n    );\n  }\n  if (hasOwn(node, \"innerComments\")) {\n    newNode.innerComments = maybeCloneComments(\n      node.innerComments,\n      deep,\n      withoutLoc,\n      commentsCache,\n    );\n  }\n  if (hasOwn(node, \"trailingComments\")) {\n    newNode.trailingComments = maybeCloneComments(\n      node.trailingComments,\n      deep,\n      withoutLoc,\n      commentsCache,\n    );\n  }\n  if (hasOwn(node, \"extra\")) {\n    newNode.extra = {\n      ...node.extra,\n    };\n  }\n\n  return newNode;\n}\n\nfunction maybeCloneComments<T extends t.Comment>(\n  comments: ReadonlyArray<T> | null,\n  deep: boolean,\n  withoutLoc: boolean,\n  commentsCache: Map<T, T>,\n): ReadonlyArray<T> | null {\n  if (!comments || !deep) {\n    return comments;\n  }\n  return comments.map(comment => {\n    const cache = commentsCache.get(comment);\n    if (cache) return cache;\n\n    const { type, value, loc } = comment;\n\n    const ret = { type, value, loc } as T;\n    if (withoutLoc) {\n      ret.loc = null;\n    }\n\n    commentsCache.set(comment, ret);\n\n    return ret;\n  });\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAD,OAAA;AAEA,MAAM;EAAEE;AAAO,CAAC,GAEZ;EAAEA,MAAM,EAAEC,QAAQ,CAACC,IAAI,CAACC,IAAI,CAACC,MAAM,CAACC,SAAS,CAACC,cAAc;AAAE,CAAC;AAKnE,SAASC,WAAWA,CAClBC,GAA8B,EAC9BC,IAAa,EACbC,UAAmB,EACnBC,aAA2B,EAC3B;EACA,IAAIH,GAAG,IAAI,OAAOA,GAAG,CAACI,IAAI,KAAK,QAAQ,EAAE;IACvC,OAAOC,iBAAiB,CAACL,GAAG,EAAEC,IAAI,EAAEC,UAAU,EAAEC,aAAa,CAAC;EAChE;EAEA,OAAOH,GAAG;AACZ;AAEA,SAASM,kBAAkBA,CACzBN,GAA8D,EAC9DC,IAAa,EACbC,UAAmB,EACnBC,aAA2B,EAC3B;EACA,IAAII,KAAK,CAACC,OAAO,CAACR,GAAG,CAAC,EAAE;IACtB,OAAOA,GAAG,CAACS,GAAG,CAACC,IAAI,IAAIX,WAAW,CAACW,IAAI,EAAET,IAAI,EAAEC,UAAU,EAAEC,aAAa,CAAC,CAAC;EAC5E;EACA,OAAOJ,WAAW,CAACC,GAAG,EAAEC,IAAI,EAAEC,UAAU,EAAEC,aAAa,CAAC;AAC1D;AAOe,SAASQ,SAASA,CAC/BD,IAAO,EACPT,IAAa,GAAG,IAAI,EACpBC,UAAmB,GAAG,KAAK,EACxB;EACH,OAAOG,iBAAiB,CAACK,IAAI,EAAET,IAAI,EAAEC,UAAU,EAAE,IAAIU,GAAG,CAAC,CAAC,CAAC;AAC7D;AAEA,SAASP,iBAAiBA,CACxBK,IAAO,EACPT,IAAa,GAAG,IAAI,EACpBC,UAAmB,GAAG,KAAK,EAC3BC,aAA2B,EACxB;EACH,IAAI,CAACO,IAAI,EAAE,OAAOA,IAAI;EAEtB,MAAM;IAAEN;EAAK,CAAC,GAAGM,IAAI;EACrB,MAAMG,OAAY,GAAG;IAAET,IAAI,EAAEM,IAAI,CAACN;EAAK,CAAC;EAGxC,IAAI,IAAAU,oBAAY,EAACJ,IAAI,CAAC,EAAE;IACtBG,OAAO,CAACE,IAAI,GAAGL,IAAI,CAACK,IAAI;IAExB,IAAIvB,MAAM,CAACkB,IAAI,EAAE,UAAU,CAAC,IAAI,OAAOA,IAAI,CAACM,QAAQ,KAAK,SAAS,EAAE;MAClEH,OAAO,CAACG,QAAQ,GAAGN,IAAI,CAACM,QAAQ;IAClC;IAEA,IAAIxB,MAAM,CAACkB,IAAI,EAAE,gBAAgB,CAAC,EAAE;MAClCG,OAAO,CAACI,cAAc,GAAGhB,IAAI,GACzBK,kBAAkB,CAChBI,IAAI,CAACO,cAAc,EACnB,IAAI,EACJf,UAAU,EACVC,aACF,CAAC,GACDO,IAAI,CAACO,cAAc;IACzB;IAEA,IAAIzB,MAAM,CAACkB,IAAI,EAAE,YAAY,CAAC,EAAE;MAC9BG,OAAO,CAACK,UAAU,GAAGjB,IAAI,GACrBK,kBAAkB,CAACI,IAAI,CAACQ,UAAU,EAAE,IAAI,EAAEhB,UAAU,EAAEC,aAAa,CAAC,GACpEO,IAAI,CAACQ,UAAU;IACrB;EACF,CAAC,MAAM,IAAI,CAAC1B,MAAM,CAAC2B,kBAAW,EAAEf,IAAI,CAAC,EAAE;IACrC,MAAM,IAAIgB,KAAK,CAAC,uBAAuBhB,IAAI,GAAG,CAAC;EACjD,CAAC,MAAM;IACL,KAAK,MAAMiB,KAAK,IAAIzB,MAAM,CAAC0B,IAAI,CAACH,kBAAW,CAACf,IAAI,CAAC,CAAC,EAAE;MAClD,IAAIZ,MAAM,CAACkB,IAAI,EAAEW,KAAK,CAAC,EAAE;QACvB,IAAIpB,IAAI,EAAE;UACRY,OAAO,CAACQ,KAAK,CAAC,GACZ,IAAAE,cAAM,EAACb,IAAI,CAAC,IAAIW,KAAK,KAAK,UAAU,GAChCG,kBAAkB,CAChBd,IAAI,CAACe,QAAQ,EACbxB,IAAI,EACJC,UAAU,EACVC,aACF,CAAC,GACDG,kBAAkB,CAEhBI,IAAI,CAACW,KAAK,CAAC,EACX,IAAI,EACJnB,UAAU,EACVC,aACF,CAAC;QACT,CAAC,MAAM;UACLU,OAAO,CAACQ,KAAK,CAAC,GAEZX,IAAI,CAACW,KAAK,CAAC;QACf;MACF;IACF;EACF;EAEA,IAAI7B,MAAM,CAACkB,IAAI,EAAE,KAAK,CAAC,EAAE;IACvB,IAAIR,UAAU,EAAE;MACdW,OAAO,CAACa,GAAG,GAAG,IAAI;IACpB,CAAC,MAAM;MACLb,OAAO,CAACa,GAAG,GAAGhB,IAAI,CAACgB,GAAG;IACxB;EACF;EACA,IAAIlC,MAAM,CAACkB,IAAI,EAAE,iBAAiB,CAAC,EAAE;IACnCG,OAAO,CAACc,eAAe,GAAGH,kBAAkB,CAC1Cd,IAAI,CAACiB,eAAe,EACpB1B,IAAI,EACJC,UAAU,EACVC,aACF,CAAC;EACH;EACA,IAAIX,MAAM,CAACkB,IAAI,EAAE,eAAe,CAAC,EAAE;IACjCG,OAAO,CAACe,aAAa,GAAGJ,kBAAkB,CACxCd,IAAI,CAACkB,aAAa,EAClB3B,IAAI,EACJC,UAAU,EACVC,aACF,CAAC;EACH;EACA,IAAIX,MAAM,CAACkB,IAAI,EAAE,kBAAkB,CAAC,EAAE;IACpCG,OAAO,CAACgB,gBAAgB,GAAGL,kBAAkB,CAC3Cd,IAAI,CAACmB,gBAAgB,EACrB5B,IAAI,EACJC,UAAU,EACVC,aACF,CAAC;EACH;EACA,IAAIX,MAAM,CAACkB,IAAI,EAAE,OAAO,CAAC,EAAE;IACzBG,OAAO,CAACiB,KAAK,GAAAlC,MAAA,CAAAmC,MAAA,KACRrB,IAAI,CAACoB,KAAK,CACd;EACH;EAEA,OAAOjB,OAAO;AAChB;AAEA,SAASW,kBAAkBA,CACzBC,QAAiC,EACjCxB,IAAa,EACbC,UAAmB,EACnBC,aAAwB,EACC;EACzB,IAAI,CAACsB,QAAQ,IAAI,CAACxB,IAAI,EAAE;IACtB,OAAOwB,QAAQ;EACjB;EACA,OAAOA,QAAQ,CAAChB,GAAG,CAACuB,OAAO,IAAI;IAC7B,MAAMC,KAAK,GAAG9B,aAAa,CAAC+B,GAAG,CAACF,OAAO,CAAC;IACxC,IAAIC,KAAK,EAAE,OAAOA,KAAK;IAEvB,MAAM;MAAE7B,IAAI;MAAE+B,KAAK;MAAET;IAAI,CAAC,GAAGM,OAAO;IAEpC,MAAMI,GAAG,GAAG;MAAEhC,IAAI;MAAE+B,KAAK;MAAET;IAAI,CAAM;IACrC,IAAIxB,UAAU,EAAE;MACdkC,GAAG,CAACV,GAAG,GAAG,IAAI;IAChB;IAEAvB,aAAa,CAACkC,GAAG,CAACL,OAAO,EAAEI,GAAG,CAAC;IAE/B,OAAOA,GAAG;EACZ,CAAC,CAAC;AACJ", "ignoreList": []}