import{f as e,r as a,j as s,ae as r,I as t,X as l,ao as i,ap as n,al as d,l as c,aq as o,k as m,K as x,i as h,F as g}from"./react-vendor-BH2w6qpU.js";import{C as u,a as j,b as p,d as f,B as b,c as N}from"./card-DfBXoe59.js";import{I as v}from"./input-CqUEtzg_.js";import{L as y}from"./label-CoNtBOlQ.js";import{T as w}from"./textarea-DKFbvSZZ.js";import{B as k}from"./badge-DqYU9d-0.js";import{S as _,e as S}from"./index-BSlVarex.js";import{N as A}from"./NasabahSidebar-DWU2nAjI.js";import{C}from"./ConfirmDialog-uGMzIHQA.js";import"./radix-vendor-CttiZxwU.js";import"./vendor-BGyKCyRD.js";import"./utils-vendor-DeZn2tlB.js";import"./query-vendor-B0Wv6VB8.js";const P=()=>{const P=e(),[T,F]=a.useState(null),[L,D]=a.useState(!0),[I,K]=a.useState(!1),[q,B]=a.useState(!1),[E,J]=a.useState(!1),[z,M]=a.useState({id:"",name:"",email:"",phone:"",address:"",date_of_birth:"",gender:"male",profile_picture:"",created_at:"",total_points:0,rank:"Bronze",total_transactions:0,total_waste_collected:0}),[O,R]=a.useState({name:"",phone:"",address:"",date_of_birth:"",gender:"male"});a.useEffect((()=>{(async()=>{D(!0),await new Promise((e=>setTimeout(e,1e3)));const e=localStorage.getItem("user");if(!e)return void P("/login");const a=JSON.parse(e);if("nasabah"!==a.role)return S.error("Akses Ditolak",{description:"Anda tidak memiliki akses ke halaman nasabah"}),void P("/");const s={id:a.id||"1",name:a.name||"Ahmad Wijaya",email:a.email||"<EMAIL>",phone:"081234567890",address:"Jl. Merdeka No. 123, Jakarta Pusat, DKI Jakarta 10110",date_of_birth:"1990-05-15",gender:"male",profile_picture:"",created_at:"2024-01-15T00:00:00Z",total_points:1250,rank:"Silver",total_transactions:18,total_waste_collected:45.2};M(s),R({name:s.name,phone:s.phone,address:s.address,date_of_birth:s.date_of_birth,gender:s.gender}),F(a),D(!1)})()}),[P]);const U=e=>new Date(e).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric"});return L?s.jsxs("div",{className:"min-h-screen bg-gray-50 flex",children:[s.jsx(A,{}),s.jsx("div",{className:"flex-1 ml-0 lg:ml-64 p-8 pt-16 lg:pt-8",children:s.jsx(_,{type:"profile"})})]}):T?s.jsxs("div",{className:"min-h-screen bg-gray-50 flex",children:[s.jsx(A,{}),s.jsx("div",{className:"flex-1 ml-0 lg:ml-64",children:s.jsxs("main",{className:"p-4 pt-16 lg:p-8 lg:pt-8",children:[s.jsxs("div",{className:"mb-8 animate-fade-in",children:[s.jsxs("h2",{className:"text-2xl font-bold text-gray-800 mb-2 flex items-center",children:[s.jsx(r,{className:"w-6 h-6 mr-3 text-bank-green-600"}),"Profil Saya"]}),s.jsx("p",{className:"text-gray-600",children:"Kelola informasi profil dan data pribadi Anda"})]}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[s.jsx("div",{className:"lg:col-span-2 space-y-6",children:s.jsxs(u,{className:"shadow-xl border-0 bg-gradient-to-br from-white to-gray-50",children:[s.jsx(j,{className:"bg-gradient-to-r from-bank-green-50 to-bank-blue-50 rounded-t-lg border-b border-gray-100",children:s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsxs("div",{children:[s.jsxs(p,{className:"text-2xl font-bold text-gray-800 flex items-center gap-3",children:[s.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-bank-green-500 to-bank-green-600 rounded-xl flex items-center justify-center",children:s.jsx(r,{className:"w-5 h-5 text-white"})}),"Informasi Profil"]}),s.jsx(f,{className:"text-gray-600 mt-2",children:"Data pribadi dan informasi akun Anda"})]}),I?s.jsxs("div",{className:"flex space-x-2",children:[s.jsxs(b,{onClick:()=>{R({name:z.name,phone:z.phone,address:z.address,date_of_birth:z.date_of_birth,gender:z.gender}),K(!1)},variant:"outline",disabled:q,className:"group",children:[s.jsx(l,{className:"w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-200"}),"Batal"]}),s.jsx(b,{onClick:()=>{J(!0)},disabled:q,className:"btn-primary group",children:q?s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Menyimpan..."]}):s.jsxs(s.Fragment,{children:[s.jsx(i,{className:"w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-200"}),"Simpan"]})})]}):s.jsxs(b,{onClick:()=>{K(!0)},className:"btn-primary group",children:[s.jsx(t,{className:"w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-200"}),"Edit Profil"]})]})}),s.jsx(N,{className:"p-6",children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center space-x-6",children:[s.jsxs("div",{className:"relative",children:[s.jsx("div",{className:"w-24 h-24 bg-gradient-to-r from-bank-green-500 to-bank-green-600 rounded-full flex items-center justify-center",children:z.profile_picture?s.jsx("img",{src:z.profile_picture,alt:"Profile",className:"w-24 h-24 rounded-full object-cover"}):s.jsx(r,{className:"w-12 h-12 text-white"})}),I&&s.jsx("button",{className:"absolute bottom-0 right-0 w-8 h-8 bg-white rounded-full shadow-lg border-2 border-gray-200 flex items-center justify-center hover:bg-gray-50 transition-colors",children:s.jsx(n,{className:"w-4 h-4 text-gray-600"})})]}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-bold text-gray-800",children:z.name}),s.jsx("p",{className:"text-gray-600",children:z.email}),s.jsxs(k,{className:`mt-2 ${(e=>{switch(e.toLowerCase()){case"bronze":return"bg-amber-100 text-amber-800 border-amber-200";case"silver":default:return"bg-gray-100 text-gray-800 border-gray-200";case"gold":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"platinum":return"bg-purple-100 text-purple-800 border-purple-200"}})(z.rank)}`,children:[s.jsx(d,{className:"w-3 h-3 mr-1"}),z.rank]})]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsxs("div",{children:[s.jsxs(y,{htmlFor:"name",className:"flex items-center gap-2 mb-2",children:[s.jsx(r,{className:"w-4 h-4 text-gray-500"}),"Nama Lengkap"]}),I?s.jsx(v,{id:"name",value:O.name,onChange:e=>R((a=>({...a,name:e.target.value}))),className:"transition-all duration-200 focus:ring-2 focus:ring-bank-green-500",required:!0}):s.jsx("div",{className:"p-3 bg-gray-50 rounded-lg border",children:z.name})]}),s.jsxs("div",{children:[s.jsxs(y,{htmlFor:"email",className:"flex items-center gap-2 mb-2",children:[s.jsx(c,{className:"w-4 h-4 text-gray-500"}),"Email"]}),s.jsxs("div",{className:"p-3 bg-gray-50 rounded-lg border flex items-center justify-between",children:[s.jsx("span",{children:z.email}),s.jsx(o,{className:"w-4 h-4 text-green-600",title:"Email terverifikasi"})]})]}),s.jsxs("div",{children:[s.jsxs(y,{htmlFor:"phone",className:"flex items-center gap-2 mb-2",children:[s.jsx(m,{className:"w-4 h-4 text-gray-500"}),"Nomor Telepon"]}),I?s.jsx(v,{id:"phone",value:O.phone,onChange:e=>R((a=>({...a,phone:e.target.value}))),className:"transition-all duration-200 focus:ring-2 focus:ring-bank-green-500",required:!0}):s.jsx("div",{className:"p-3 bg-gray-50 rounded-lg border",children:z.phone})]}),s.jsxs("div",{children:[s.jsxs(y,{htmlFor:"date_of_birth",className:"flex items-center gap-2 mb-2",children:[s.jsx(x,{className:"w-4 h-4 text-gray-500"}),"Tanggal Lahir"]}),I?s.jsx(v,{id:"date_of_birth",type:"date",value:O.date_of_birth,onChange:e=>R((a=>({...a,date_of_birth:e.target.value}))),className:"transition-all duration-200 focus:ring-2 focus:ring-bank-green-500",required:!0}):s.jsx("div",{className:"p-3 bg-gray-50 rounded-lg border",children:U(z.date_of_birth)})]}),s.jsxs("div",{className:"md:col-span-2",children:[s.jsxs(y,{htmlFor:"address",className:"flex items-center gap-2 mb-2",children:[s.jsx(h,{className:"w-4 h-4 text-gray-500"}),"Alamat"]}),I?s.jsx(w,{id:"address",value:O.address,onChange:e=>R((a=>({...a,address:e.target.value}))),className:"transition-all duration-200 focus:ring-2 focus:ring-bank-green-500",rows:3,required:!0}):s.jsx("div",{className:"p-3 bg-gray-50 rounded-lg border",children:z.address})]}),s.jsxs("div",{children:[s.jsxs(y,{htmlFor:"gender",className:"flex items-center gap-2 mb-2",children:[s.jsx(r,{className:"w-4 h-4 text-gray-500"}),"Jenis Kelamin"]}),I?s.jsxs("select",{id:"gender",value:O.gender,onChange:e=>R((a=>({...a,gender:e.target.value}))),className:"w-full p-3 border border-gray-300 rounded-lg transition-all duration-200 focus:ring-2 focus:ring-bank-green-500 focus:border-transparent",children:[s.jsx("option",{value:"male",children:"Laki-laki"}),s.jsx("option",{value:"female",children:"Perempuan"})]}):s.jsx("div",{className:"p-3 bg-gray-50 rounded-lg border",children:"male"===z.gender?"Laki-laki":"Perempuan"})]}),s.jsxs("div",{children:[s.jsxs(y,{className:"flex items-center gap-2 mb-2",children:[s.jsx(x,{className:"w-4 h-4 text-gray-500"}),"Bergabung Sejak"]}),s.jsx("div",{className:"p-3 bg-gray-50 rounded-lg border",children:U(z.created_at)})]})]})]})})]})}),s.jsxs("div",{className:"space-y-6",children:[s.jsxs(u,{className:"hover:shadow-lg transition-shadow duration-300",children:[s.jsx(j,{children:s.jsxs(p,{className:"flex items-center",children:[s.jsx(g,{className:"w-5 h-5 mr-2 text-yellow-600"}),"Statistik Saya"]})}),s.jsx(N,{children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-green-800",children:"Total Poin"}),s.jsx("p",{className:"text-xl font-bold text-green-600",children:z.total_points.toLocaleString()})]}),s.jsx(g,{className:"w-8 h-8 text-green-600"})]}),s.jsxs("div",{className:"flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-blue-800",children:"Total Transaksi"}),s.jsx("p",{className:"text-xl font-bold text-blue-600",children:z.total_transactions})]}),s.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:s.jsx("span",{className:"text-blue-600 font-bold text-sm",children:z.total_transactions})})]}),s.jsxs("div",{className:"flex items-center justify-between p-3 bg-purple-50 rounded-lg border border-purple-200",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-purple-800",children:"Sampah Terkumpul"}),s.jsxs("p",{className:"text-xl font-bold text-purple-600",children:[z.total_waste_collected," Kg"]})]}),s.jsx("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:s.jsx("span",{className:"text-purple-600 font-bold text-xs",children:"Kg"})})]})]})})]}),s.jsxs(u,{className:"hover:shadow-lg transition-shadow duration-300",children:[s.jsx(j,{children:s.jsxs(p,{className:"flex items-center",children:[s.jsx(o,{className:"w-5 h-5 mr-2 text-green-600"}),"Keamanan Akun"]})}),s.jsx(N,{children:s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:s.jsx(c,{className:"w-4 h-4 text-green-600"})}),s.jsxs("div",{children:[s.jsx("p",{className:"font-medium text-gray-800",children:"Email Terverifikasi"}),s.jsx("p",{className:"text-sm text-gray-500",children:"Akun Anda aman"})]})]}),s.jsx(k,{className:"bg-green-100 text-green-800 border-green-200",children:"✓ Aktif"})]}),s.jsxs("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:s.jsx(m,{className:"w-4 h-4 text-blue-600"})}),s.jsxs("div",{children:[s.jsx("p",{className:"font-medium text-gray-800",children:"Nomor Telepon"}),s.jsx("p",{className:"text-sm text-gray-500",children:"Terdaftar dan aktif"})]})]}),s.jsx(k,{className:"bg-blue-100 text-blue-800 border-blue-200",children:"✓ Aktif"})]})]})})]}),s.jsxs(u,{className:"hover:shadow-lg transition-shadow duration-300",children:[s.jsx(j,{children:s.jsx(p,{children:"Aksi Cepat"})}),s.jsx(N,{children:s.jsxs("div",{className:"space-y-3",children:[s.jsxs(b,{variant:"outline",className:"w-full justify-start hover-scale",onClick:()=>S.info("Fitur Ubah Password",{description:"Akan segera tersedia"}),children:[s.jsx(o,{className:"w-4 h-4 mr-3 text-gray-600"}),"Ubah Password"]}),s.jsxs(b,{variant:"outline",className:"w-full justify-start hover-scale",onClick:()=>S.info("Fitur Riwayat Aktivitas",{description:"Akan segera tersedia"}),children:[s.jsx(x,{className:"w-4 h-4 mr-3 text-gray-600"}),"Riwayat Aktivitas"]}),s.jsxs(b,{variant:"outline",className:"w-full justify-start hover-scale",onClick:()=>S.info("Fitur Pengaturan Notifikasi",{description:"Akan segera tersedia"}),children:[s.jsx(c,{className:"w-4 h-4 mr-3 text-gray-600"}),"Pengaturan Notifikasi"]})]})})]})]})]})]})}),s.jsx(C,{isOpen:E,onClose:()=>J(!1),onConfirm:async()=>{B(!0),J(!1);try{await new Promise((e=>setTimeout(e,1500)));const e={...z,...O};M(e),K(!1),S.success("Profil berhasil diperbarui!",{description:"Informasi profil Anda telah disimpan"})}catch(e){S.error("Gagal memperbarui profil",{description:"Terjadi kesalahan saat menyimpan data"})}finally{B(!1)}},title:"Simpan Perubahan Profil",description:"Apakah Anda yakin ingin menyimpan perubahan pada profil Anda? Pastikan semua informasi sudah benar.",confirmText:"Simpan",cancelText:"Batal",type:"success"})]}):null};export{P as default};
