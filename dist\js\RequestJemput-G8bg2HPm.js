import{f as e,r as s,j as a,y as t,K as r,aa as n,i,k as l,ae as d,x as c,H as m,J as o,ab as x}from"./react-vendor-BH2w6qpU.js";import{N as h}from"./NasabahSidebar-DWU2nAjI.js";import{S as g,e as u}from"./index-BSlVarex.js";import{C as p,a as j,b,c as N,B as y}from"./card-DfBXoe59.js";import{B as f}from"./badge-DqYU9d-0.js";import{I as v}from"./input-CqUEtzg_.js";import{L as k}from"./label-CoNtBOlQ.js";import{T as w}from"./textarea-DKFbvSZZ.js";import{C as _}from"./ConfirmDialog-uGMzIHQA.js";import"./radix-vendor-CttiZxwU.js";import"./vendor-BGyKCyRD.js";import"./utils-vendor-DeZn2tlB.js";import"./query-vendor-B0Wv6VB8.js";const S=()=>{const S=e(),[q,C]=s.useState(!0),[T,D]=s.useState(!1),[P,R]=s.useState(!1),[F,A]=s.useState("new"),[B,J]=s.useState({date:"",time_slot:"",address:"",phone:"",notes:""}),[M,I]=s.useState([{id:"1",type:"",estimated_weight:0,description:""}]),[K]=s.useState([{id:"REQ001",date:"2024-06-15",time_slot:"09:00-12:00",address:"Jl. Merdeka No. 123, Jakarta Pusat",phone:"081234567890",notes:"Sampah di depan rumah",waste_items:[{id:"1",type:"Plastik",estimated_weight:5,description:"Botol plastik bekas"}],status:"pending",created_at:"2024-06-14"}]),O=["Plastik","Kertas","Logam","Kaca","Elektronik","Organik"];s.useEffect((()=>{(async()=>{C(!0),await new Promise((e=>setTimeout(e,1e3)));const e=localStorage.getItem("user");if(!e)return void S("/login");const s=JSON.parse(e);"nasabah"===s.role?(J((e=>({...e,phone:s.phone||"",address:s.address||""}))),C(!1)):S("/login")})()}),[S]);const L=(e,s,a)=>{I(M.map((t=>t.id===e?{...t,[s]:a}:t)))},z=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"confirmed":return"bg-blue-100 text-blue-800 border-blue-200";case"in_progress":return"bg-purple-100 text-purple-800 border-purple-200";case"completed":return"bg-green-100 text-green-800 border-green-200";case"cancelled":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},E=e=>{switch(e){case"pending":return"Menunggu";case"confirmed":return"Dikonfirmasi";case"in_progress":return"Dalam Proses";case"completed":return"Selesai";case"cancelled":return"Dibatalkan";default:return e}},H=e=>new Date(e).toLocaleDateString("id-ID",{day:"numeric",month:"long",year:"numeric"});return q?a.jsxs("div",{className:"min-h-screen bg-gray-50",children:[a.jsx(h,{}),a.jsx("div",{className:"lg:ml-64",children:a.jsx("main",{className:"p-4 pt-16 lg:pt-8",children:a.jsx(g,{type:"request-jemput"})})})]}):a.jsxs("div",{className:"min-h-screen bg-gray-50",children:[a.jsx(h,{}),a.jsx("div",{className:"lg:ml-64",children:a.jsxs("main",{className:"p-4 pt-16 lg:pt-8 space-y-6",children:[a.jsxs("div",{children:[a.jsxs("h1",{className:"text-2xl lg:text-3xl font-bold text-gray-800 flex items-center",children:[a.jsx(t,{className:"w-8 h-8 mr-3 text-bank-green-600"}),"Request Jemput Sampah"]}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Ajukan permintaan penjemputan sampah ke lokasi Anda"})]}),a.jsxs("div",{className:"flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit",children:[a.jsx("button",{onClick:()=>A("new"),className:"px-4 py-2 rounded-md text-sm font-medium transition-colors "+("new"===F?"bg-white text-bank-green-700 shadow-sm":"text-gray-600 hover:text-gray-800"),children:"Request Baru"}),a.jsx("button",{onClick:()=>A("history"),className:"px-4 py-2 rounded-md text-sm font-medium transition-colors "+("history"===F?"bg-white text-bank-green-700 shadow-sm":"text-gray-600 hover:text-gray-800"),children:"Riwayat Request"})]}),"new"===F?a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[a.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[a.jsxs(p,{children:[a.jsx(j,{children:a.jsxs(b,{className:"flex items-center",children:[a.jsx(r,{className:"w-5 h-5 mr-2 text-blue-600"}),"Informasi Penjemputan"]})}),a.jsxs(N,{className:"space-y-4",children:[a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[a.jsxs("div",{children:[a.jsxs(k,{htmlFor:"date",className:"flex items-center gap-2 mb-2",children:[a.jsx(r,{className:"w-4 h-4 text-gray-500"}),"Tanggal Penjemputan *"]}),a.jsx(v,{id:"date",type:"date",value:B.date,onChange:e=>J((s=>({...s,date:e.target.value}))),min:(new Date).toISOString().split("T")[0],className:"transition-all duration-200 focus:ring-2 focus:ring-bank-green-500",required:!0})]}),a.jsxs("div",{children:[a.jsxs(k,{htmlFor:"time_slot",className:"flex items-center gap-2 mb-2",children:[a.jsx(n,{className:"w-4 h-4 text-gray-500"}),"Waktu Penjemputan *"]}),a.jsxs("select",{id:"time_slot",value:B.time_slot,onChange:e=>J((s=>({...s,time_slot:e.target.value}))),className:"w-full p-3 border border-gray-300 rounded-lg transition-all duration-200 focus:ring-2 focus:ring-bank-green-500 focus:border-transparent",required:!0,children:[a.jsx("option",{value:"",children:"Pilih waktu"}),["08:00-11:00","09:00-12:00","13:00-16:00","14:00-17:00"].map((e=>a.jsx("option",{value:e,children:e},e)))]})]})]}),a.jsxs("div",{children:[a.jsxs(k,{htmlFor:"address",className:"flex items-center gap-2 mb-2",children:[a.jsx(i,{className:"w-4 h-4 text-gray-500"}),"Alamat Penjemputan *"]}),a.jsx(w,{id:"address",value:B.address,onChange:e=>J((s=>({...s,address:e.target.value}))),placeholder:"Masukkan alamat lengkap untuk penjemputan",className:"transition-all duration-200 focus:ring-2 focus:ring-bank-green-500",rows:3,required:!0})]}),a.jsxs("div",{children:[a.jsxs(k,{htmlFor:"phone",className:"flex items-center gap-2 mb-2",children:[a.jsx(l,{className:"w-4 h-4 text-gray-500"}),"Nomor Telepon *"]}),a.jsx(v,{id:"phone",value:B.phone,onChange:e=>J((s=>({...s,phone:e.target.value}))),placeholder:"Nomor telepon yang bisa dihubungi",className:"transition-all duration-200 focus:ring-2 focus:ring-bank-green-500",required:!0})]}),a.jsxs("div",{children:[a.jsxs(k,{htmlFor:"notes",className:"flex items-center gap-2 mb-2",children:[a.jsx(d,{className:"w-4 h-4 text-gray-500"}),"Catatan Tambahan"]}),a.jsx(w,{id:"notes",value:B.notes,onChange:e=>J((s=>({...s,notes:e.target.value}))),placeholder:"Catatan khusus untuk petugas (opsional)",className:"transition-all duration-200 focus:ring-2 focus:ring-bank-green-500",rows:2})]})]})]}),a.jsxs(p,{children:[a.jsx(j,{children:a.jsxs(b,{className:"flex items-center justify-between",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx(c,{className:"w-5 h-5 mr-2 text-green-600"}),"Jenis Sampah"]}),a.jsxs(y,{type:"button",variant:"outline",size:"sm",onClick:()=>{const e={id:Date.now().toString(),type:"",estimated_weight:0,description:""};I([...M,e])},className:"hover-scale",children:[a.jsx(m,{className:"w-4 h-4 mr-1"}),"Tambah"]})]})}),a.jsx(N,{children:a.jsx("div",{className:"space-y-4",children:M.map(((e,s)=>a.jsxs("div",{className:"p-4 border border-gray-200 rounded-lg",children:[a.jsxs("div",{className:"flex items-center justify-between mb-3",children:[a.jsxs("h4",{className:"font-medium text-gray-800",children:["Sampah #",s+1]}),M.length>1&&a.jsx(y,{type:"button",variant:"ghost",size:"sm",onClick:()=>{return s=e.id,void(M.length>1&&I(M.filter((e=>e.id!==s))));var s},className:"text-red-600 hover:text-red-700 hover:bg-red-50",children:a.jsx(o,{className:"w-4 h-4"})})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[a.jsxs("div",{children:[a.jsx(k,{className:"text-sm font-medium text-gray-700 mb-1 block",children:"Jenis Sampah *"}),a.jsxs("select",{value:e.type,onChange:s=>L(e.id,"type",s.target.value),className:"w-full p-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-bank-green-500 focus:border-transparent",required:!0,children:[a.jsx("option",{value:"",children:"Pilih jenis"}),O.map((e=>a.jsx("option",{value:e,children:e},e)))]})]}),a.jsxs("div",{children:[a.jsx(k,{className:"text-sm font-medium text-gray-700 mb-1 block",children:"Perkiraan Berat (kg) *"}),a.jsx(v,{type:"number",step:"0.1",min:"0.1",value:e.estimated_weight||"",onChange:s=>L(e.id,"estimated_weight",parseFloat(s.target.value)||0),placeholder:"0.0",className:"text-sm",required:!0})]})]}),a.jsxs("div",{className:"mt-3",children:[a.jsx(k,{className:"text-sm font-medium text-gray-700 mb-1 block",children:"Deskripsi"}),a.jsx(v,{value:e.description,onChange:s=>L(e.id,"description",s.target.value),placeholder:"Deskripsi singkat sampah (opsional)",className:"text-sm"})]})]},e.id)))})})]})]}),a.jsx("div",{children:a.jsxs(p,{className:"sticky top-4",children:[a.jsx(j,{children:a.jsxs(b,{className:"flex items-center",children:[a.jsx(x,{className:"w-5 h-5 mr-2 text-green-600"}),"Ringkasan Request"]})}),a.jsxs(N,{className:"space-y-4",children:[a.jsxs("div",{className:"space-y-3 text-sm",children:[a.jsxs("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"Tanggal:"}),a.jsx("span",{className:"font-medium",children:B.date?H(B.date):"-"})]}),a.jsxs("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"Waktu:"}),a.jsx("span",{className:"font-medium",children:B.time_slot||"-"})]}),a.jsxs("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"Total Jenis:"}),a.jsxs("span",{className:"font-medium",children:[M.filter((e=>e.type)).length," jenis"]})]}),a.jsxs("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"Total Berat:"}),a.jsxs("span",{className:"font-medium",children:[M.reduce(((e,s)=>e+(s.estimated_weight||0)),0).toFixed(1)," kg"]})]})]}),a.jsx("div",{className:"pt-4 border-t border-gray-200",children:a.jsx(y,{onClick:()=>{(()=>{if(!(B.date&&B.time_slot&&B.address&&B.phone))return u.error("Form Tidak Lengkap",{description:"Mohon lengkapi semua field yang wajib diisi"}),!1;if(0===M.filter((e=>e.type&&e.estimated_weight>0)).length)return u.error("Sampah Tidak Valid",{description:"Minimal harus ada 1 jenis sampah dengan berat > 0"}),!1;const e=new Date(B.date),s=new Date;return s.setHours(0,0,0,0),!(e<s&&(u.error("Tanggal Tidak Valid",{description:"Tanggal penjemputan tidak boleh di masa lalu"}),1))})()&&R(!0)},disabled:T,className:"w-full bg-bank-green-600 hover:bg-bank-green-700 text-white hover-scale",children:T?a.jsxs(a.Fragment,{children:[a.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Mengirim..."]}):a.jsxs(a.Fragment,{children:[a.jsx(t,{className:"w-4 h-4 mr-2"}),"Kirim Request"]})})}),a.jsxs("div",{className:"text-xs text-gray-500 bg-blue-50 p-3 rounded-lg",children:[a.jsx("strong",{children:"Catatan:"}),' Setelah request dikirim, status akan menjadi "Menunggu" dan tim kami akan menghubungi Anda untuk konfirmasi.']})]})]})})]}):a.jsxs(p,{children:[a.jsx(j,{children:a.jsx(b,{children:"Riwayat Request Penjemputan"})}),a.jsx(N,{children:0===K.length?a.jsxs("div",{className:"text-center py-12",children:[a.jsx(t,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-600 mb-2",children:"Belum ada request"}),a.jsx("p",{className:"text-gray-500",children:"Anda belum pernah mengajukan request penjemputan"})]}):a.jsx("div",{className:"space-y-4",children:K.map((e=>a.jsxs("div",{className:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200",children:[a.jsxs("div",{className:"flex items-start justify-between mb-3",children:[a.jsxs("div",{children:[a.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[a.jsx("h4",{className:"font-medium text-gray-800",children:e.id}),a.jsx(f,{className:z(e.status),children:E(e.status)})]}),a.jsxs("p",{className:"text-sm text-gray-600",children:[H(e.date)," • ",e.time_slot]})]}),a.jsx(y,{variant:"ghost",size:"sm",onClick:()=>u.info("Detail Request",{description:"Akan segera tersedia"}),className:"hover-scale",children:"Detail"})]}),a.jsxs("div",{className:"space-y-2 text-sm",children:[a.jsxs("div",{className:"flex items-start gap-2",children:[a.jsx(i,{className:"w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0"}),a.jsx("span",{className:"text-gray-600",children:e.address})]}),a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx(l,{className:"w-4 h-4 text-gray-400"}),a.jsx("span",{className:"text-gray-600",children:e.phone})]}),a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx(c,{className:"w-4 h-4 text-gray-400"}),a.jsxs("span",{className:"text-gray-600",children:[e.waste_items.length," jenis sampah •",e.waste_items.reduce(((e,s)=>e+s.estimated_weight),0)," kg"]})]})]}),e.notes&&a.jsxs("div",{className:"mt-3 p-2 bg-gray-50 rounded text-sm text-gray-600",children:[a.jsx("strong",{children:"Catatan:"})," ",e.notes]})]},e.id)))})})]})]})}),a.jsx(_,{isOpen:P,onClose:()=>R(!1),onConfirm:async()=>{D(!0),R(!1);try{await new Promise((e=>setTimeout(e,2e3)));Date.now(),B.date,B.time_slot,B.address,B.phone,B.notes,M.filter((e=>e.type&&e.estimated_weight>0)),(new Date).toISOString();u.success("Request Berhasil Dikirim!",{description:"Permintaan penjemputan Anda telah diterima dan sedang diproses"}),J({date:"",time_slot:"",address:"",phone:"",notes:""}),I([{id:"1",type:"",estimated_weight:0,description:""}]),A("history")}catch(e){u.error("Gagal Mengirim Request",{description:"Terjadi kesalahan saat mengirim permintaan. Silakan coba lagi."})}finally{D(!1)}},title:"Konfirmasi Request Penjemputan",description:"Apakah Anda yakin ingin mengirim request penjemputan ini? Pastikan semua informasi sudah benar.",confirmText:"Kirim Request",cancelText:"Batal",type:"success"})]})};export{S as default};
