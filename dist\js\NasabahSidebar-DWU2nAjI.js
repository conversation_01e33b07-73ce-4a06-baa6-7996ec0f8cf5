import{f as e,r as a,j as s,g as t,X as r,F as i,w as n,ae as l,am as d,y as o,an as c,ag as h,ah as x,ai as m}from"./react-vendor-BH2w6qpU.js";import{B as g}from"./card-DfBXoe59.js";import{e as b}from"./index-BSlVarex.js";const p=()=>{const p=e(),[f,j]=a.useState(!0);a.useEffect((()=>{const e=()=>{window.innerWidth>=1024?j(!1):j(!0)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)}),[]);const u=[{title:"Dashboard",path:"/nasabah/dashboard",icon:n},{title:"<PERSON>il Say<PERSON>",path:"/nasabah/profil",icon:l},{title:"Riwayat Transaksi",path:"/nasabah/riwayat",icon:d},{title:"Request Jemput",path:"/nasabah/jemput",icon:o},{title:"Tukar Poin",path:"/nasabah/tukar-poin",icon:c}];return s.jsxs(s.Fragment,{children:[!f&&s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:()=>j(!0)}),s.jsxs("div",{className:`\n        fixed top-0 left-0 h-full bg-white shadow-xl border-r border-gray-200 z-50 transform transition-transform duration-300 ease-in-out flex flex-col\n        ${f?"-translate-x-full lg:translate-x-0 lg:w-16":"translate-x-0 w-64"}\n        lg:fixed lg:transform-none\n      `,children:[s.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[!f&&s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-8 h-8 bg-gradient-green rounded-lg flex items-center justify-center",children:s.jsx(t,{className:"w-5 h-5 text-white"})}),s.jsxs("div",{children:[s.jsx("h2",{className:"font-bold text-gray-800",children:"Dashboard"}),s.jsx("p",{className:"text-xs text-gray-500",children:"Bank Sampah Digital"})]})]}),s.jsx(g,{variant:"ghost",size:"sm",onClick:()=>j(!f),className:"lg:hidden",children:s.jsx(r,{className:"w-4 h-4"})})]}),!f&&s.jsx("div",{className:"p-4 border-b border-gray-200",children:s.jsx("div",{className:"bg-gradient-to-r from-bank-green-600 to-bank-green-700 rounded-xl p-4 text-white shadow-lg",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-xs opacity-90 font-medium",children:"Total Poin"}),s.jsx("p",{className:"text-2xl font-bold mt-1",children:1250..toLocaleString()}),s.jsx("p",{className:"text-xs opacity-75 mt-1",children:"Siap ditukar"})]}),s.jsx("div",{className:"w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center",children:s.jsx(i,{className:"w-6 h-6 text-white"})})]})})}),s.jsxs("div",{className:"flex flex-col flex-1 p-4",children:[s.jsx("nav",{className:"space-y-2 flex-1",children:u.map((e=>s.jsxs(h,{to:e.path,className:({isActive:e})=>"flex items-center space-x-3 px-3 py-2.5 rounded-lg transition-all duration-200 group "+(e?"bg-bank-green-100 text-bank-green-700 border border-bank-green-200":"text-gray-600 hover:bg-gray-100 hover:text-gray-800"),onClick:()=>{window.innerWidth<1024&&j(!0);["/nasabah/dashboard","/nasabah/profil","/nasabah/riwayat","/nasabah/jemput","/nasabah/tukar-poin"].includes(e.path)||b.info("Fitur Dalam Pengembangan",{description:`${e.title} akan segera tersedia`})},children:[s.jsx(e.icon,{className:"w-5 h-5 flex-shrink-0"}),!f&&s.jsx("span",{className:"font-medium truncate",children:e.title})]},e.path)))}),s.jsx("div",{className:"pt-4 border-t border-gray-200 mt-4",children:s.jsxs(g,{variant:"ghost",onClick:()=>{localStorage.removeItem("user"),b.success("Logout Berhasil",{description:"Anda telah keluar dari sistem"}),p("/")},className:"w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50",children:[s.jsx(x,{className:"w-5 h-5 mr-3"}),!f&&s.jsx("span",{children:"Keluar"})]})})]})]}),s.jsx(g,{variant:"ghost",size:"sm",onClick:()=>j(!1),className:"fixed top-4 left-4 z-30 lg:hidden "+(f?"block":"hidden"),children:s.jsx(m,{className:"w-5 h-5"})})]})};export{p as N};
