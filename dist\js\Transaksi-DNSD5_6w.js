import{f as e,r as a,j as s,S as t,ab as r,aa as i,y as n,h as l,F as d,ac as o,I as c,G as m,H as x}from"./react-vendor-C9kdeAq4.js";import{C as h,c as g,a as u,b as j,d as p,B as _}from"./card-l7WlC16I.js";import{I as b}from"./input-B25hGi49.js";import{L as N}from"./label-ZSTvXwUz.js";import{T as f,a as w,b as v,c as y,d as k,e as T}from"./table-qPaxNgV0.js";import{D as S,a as C,b as F,c as D,d as q,e as B,C as P}from"./ConfirmDialog-KlxVkBeS.js";import{B as I}from"./badge-8i3m94Oh.js";import{S as K,e as A}from"./index-BZfOZLOv.js";import{A as H}from"./AdminSidebar-y32hu_7f.js";import"./radix-vendor-CttiZxwU.js";import"./vendor-C0DTsUaw.js";import"./utils-vendor-DeZn2tlB.js";import"./query-vendor-B0Wv6VB8.js";const R=()=>{const R=e(),[O,E]=a.useState(null),[J,L]=a.useState(!0),[Z,$]=a.useState([]),[W,z]=a.useState(!1),[G,M]=a.useState(!1),[Q,U]=a.useState(null),[V,X]=a.useState(null),[Y,ee]=a.useState({user_name:"",waste_name:"",weight:"",total_price:"",total_points:"",date:""}),ae=[{id:"1",user_id:"1",user_name:"Siti Nurhaliza",waste_id:"1",waste_name:"Plastik Botol",weight:5.5,total_price:27500,total_points:55,date:"2024-01-15",created_at:"2024-01-15T10:00:00Z"},{id:"2",user_id:"2",user_name:"Budi Santoso",waste_id:"2",waste_name:"Kertas Bekas",weight:8.2,total_price:24600,total_points:82,date:"2024-01-16",created_at:"2024-01-16T14:30:00Z"},{id:"3",user_id:"3",user_name:"Ahmad Wijaya",waste_id:"3",waste_name:"Logam Bekas",weight:12,total_price:12e4,total_points:240,date:"2024-01-17",created_at:"2024-01-17T09:15:00Z"},{id:"4",user_id:"1",user_name:"Siti Nurhaliza",waste_id:"4",waste_name:"Kardus Bekas",weight:3.8,total_price:11400,total_points:38,date:"2024-01-18",created_at:"2024-01-18T16:45:00Z"}];a.useEffect((()=>{(async()=>{L(!0),await new Promise((e=>setTimeout(e,1500)));const e=localStorage.getItem("user");if(!e)return void R("/login");const a=JSON.parse(e);if("admin"!==a.role)return A.error("Akses Ditolak",{description:"Anda tidak memiliki akses ke halaman admin"}),void R("/");E(a),$(ae),L(!1)})()}),[R]);const se=e=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(e),te=e=>{if(e.preventDefault(),Q){const e={...Q,user_name:Y.user_name,waste_name:Y.waste_name,weight:parseFloat(Y.weight),total_price:parseFloat(Y.total_price),total_points:parseInt(Y.total_points),date:Y.date};$((a=>a.map((a=>a.id===Q.id?e:a)))),A.success("Transaksi berhasil diperbarui!",{description:`Transaksi ${Y.user_name} telah diperbaharui`}),M(!1)}else{const e={id:Date.now().toString(),user_id:Date.now().toString(),user_name:Y.user_name,waste_id:Date.now().toString(),waste_name:Y.waste_name,weight:parseFloat(Y.weight),total_price:parseFloat(Y.total_price),total_points:parseInt(Y.total_points),date:Y.date,created_at:(new Date).toISOString()};$((a=>[...a,e])),A.success("Transaksi baru berhasil ditambahkan!",{description:`Transaksi ${Y.user_name} telah terdaftar`}),z(!1)}U(null)},re={total:Z.length,totalRevenue:Z.reduce(((e,a)=>e+a.total_price),0),totalWeight:Z.reduce(((e,a)=>e+a.weight),0),totalPoints:Z.reduce(((e,a)=>e+a.total_points),0),avgTransaction:Z.length>0?Z.reduce(((e,a)=>e+a.total_price),0)/Z.length:0};return J?s.jsxs("div",{className:"min-h-screen bg-gray-50 flex",children:[s.jsx(H,{}),s.jsx("div",{className:"flex-1 lg:ml-0 p-4 lg:p-8 pt-16 lg:pt-8",children:s.jsx(K,{type:"table"})})]}):O?s.jsxs("div",{className:"min-h-screen bg-gray-50 flex",children:[s.jsx(H,{}),s.jsx("div",{className:"flex-1 lg:ml-0",children:s.jsxs("main",{className:"p-4 lg:p-8",children:[s.jsxs("div",{className:"mb-8",children:[s.jsxs("h2",{className:"text-2xl font-bold text-gray-800 mb-2 flex items-center",children:[s.jsx(t,{className:"w-6 h-6 mr-3 text-bank-green-600"}),"Kelola Transaksi"]}),s.jsx("p",{className:"text-gray-600",children:"Kelola semua transaksi penjualan sampah"})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8",children:[s.jsx(h,{className:"hover:shadow-lg transition-shadow duration-300",children:s.jsx(g,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Total Transaksi"}),s.jsx("p",{className:"text-2xl font-bold text-gray-900",children:re.total})]}),s.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-bank-green-100 to-bank-green-200 rounded-xl flex items-center justify-center",children:s.jsx(t,{className:"w-6 h-6 text-bank-green-600"})})]})})}),s.jsx(h,{className:"hover:shadow-lg transition-shadow duration-300",children:s.jsx(g,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Total Pendapatan"}),s.jsx("p",{className:"text-2xl font-bold text-green-600",children:se(re.totalRevenue)})]}),s.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-green-100 to-green-200 rounded-xl flex items-center justify-center",children:s.jsx(r,{className:"w-6 h-6 text-green-600"})})]})})}),s.jsx(h,{className:"hover:shadow-lg transition-shadow duration-300",children:s.jsx(g,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Total Berat"}),s.jsxs("p",{className:"text-2xl font-bold text-blue-600",children:[re.totalWeight.toFixed(1)," Kg"]})]}),s.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-100 to-blue-200 rounded-xl flex items-center justify-center",children:s.jsx(i,{className:"w-6 h-6 text-blue-600"})})]})})}),s.jsx(h,{className:"hover:shadow-lg transition-shadow duration-300",children:s.jsx(g,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Total Poin"}),s.jsx("p",{className:"text-2xl font-bold text-purple-600",children:re.totalPoints.toLocaleString()})]}),s.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-purple-100 to-purple-200 rounded-xl flex items-center justify-center",children:s.jsx(n,{className:"w-6 h-6 text-purple-600"})})]})})}),s.jsx(h,{className:"hover:shadow-lg transition-shadow duration-300",children:s.jsx(g,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"Rata-rata"}),s.jsx("p",{className:"text-2xl font-bold text-orange-600",children:se(re.avgTransaction)})]}),s.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-orange-100 to-orange-200 rounded-xl flex items-center justify-center",children:s.jsx(l,{className:"w-6 h-6 text-orange-600"})})]})})})]}),s.jsxs(h,{className:"shadow-xl border-0 bg-gradient-to-br from-white to-gray-50",children:[s.jsx(u,{className:"bg-gradient-to-r from-bank-green-50 to-bank-blue-50 rounded-t-lg border-b border-gray-100",children:s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsxs("div",{children:[s.jsxs(j,{className:"text-2xl font-bold text-gray-800 flex items-center gap-3",children:[s.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-bank-green-500 to-bank-green-600 rounded-xl flex items-center justify-center",children:s.jsx(t,{className:"w-5 h-5 text-white"})}),"Daftar Transaksi"]}),s.jsx(p,{className:"text-gray-600 mt-2",children:"Kelola transaksi penjualan sampah dan pembayaran"})]}),s.jsxs("button",{onClick:()=>{ee({user_name:"",waste_name:"",weight:"",total_price:"",total_points:"",date:""}),z(!0)},className:"btn-add group",children:[s.jsx(d,{className:"w-5 h-5 group-hover:rotate-90 transition-transform duration-300"}),"Tambah Transaksi"]})]})}),s.jsx(g,{className:"p-0",children:s.jsxs(f,{children:[s.jsx(w,{children:s.jsxs(v,{className:"bg-gray-50/50",children:[s.jsx(y,{className:"font-semibold text-gray-700",children:"Nasabah"}),s.jsx(y,{className:"font-semibold text-gray-700",children:"Jenis Sampah"}),s.jsx(y,{className:"font-semibold text-gray-700",children:"Berat"}),s.jsx(y,{className:"font-semibold text-gray-700",children:"Total Harga"}),s.jsx(y,{className:"font-semibold text-gray-700",children:"Poin"}),s.jsx(y,{className:"font-semibold text-gray-700",children:"Tanggal"}),s.jsx(y,{className:"font-semibold text-gray-700",children:"Aksi"})]})}),s.jsx(k,{children:Z.map((e=>s.jsxs(v,{className:"hover:bg-gray-50/50 transition-colors",children:[s.jsx(T,{className:"font-medium text-gray-800",children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(o,{className:"w-4 h-4 text-gray-400"}),e.user_name]})}),s.jsx(T,{className:"text-gray-600",children:e.waste_name}),s.jsx(T,{className:"text-gray-600",children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(i,{className:"w-4 h-4 text-gray-400"}),e.weight," Kg"]})}),s.jsx(T,{className:"text-gray-600",children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(r,{className:"w-4 h-4 text-gray-400"}),s.jsx("span",{className:"font-semibold text-green-600",children:se(e.total_price)})]})}),s.jsx(T,{children:s.jsxs(I,{className:"bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800 flex items-center gap-1 w-fit",children:[s.jsx(n,{className:"w-3 h-3"}),e.total_points]})}),s.jsx(T,{className:"text-gray-600",children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(c,{className:"w-4 h-4 text-gray-400"}),new Date(e.date).toLocaleDateString("id-ID")]})}),s.jsx(T,{children:s.jsxs("div",{className:"flex space-x-2",children:[s.jsxs("button",{className:"btn-edit group flex items-center gap-2 px-3 py-2 text-sm rounded-lg",onClick:()=>(e=>{U(e),ee({user_name:e.user_name,waste_name:e.waste_name,weight:e.weight.toString(),total_price:e.total_price.toString(),total_points:e.total_points.toString(),date:e.date}),M(!0)})(e),title:"Edit Transaksi",children:[s.jsx(m,{className:"w-4 h-4 group-hover:scale-110 transition-transform duration-200"}),"Edit"]}),s.jsxs("button",{className:"btn-delete group flex items-center gap-2 px-3 py-2 text-sm rounded-lg",onClick:()=>(e=>{X(e)})(e),title:"Hapus Transaksi",children:[s.jsx(x,{className:"w-4 h-4 group-hover:scale-110 transition-transform duration-200"}),"Hapus"]})]})})]},e.id)))})]})})]})]})}),s.jsx(S,{open:W,onOpenChange:z,children:s.jsxs(C,{className:"sm:max-w-md",children:[s.jsxs(F,{children:[s.jsxs(D,{className:"flex items-center gap-2",children:[s.jsx(d,{className:"w-5 h-5 text-bank-green-600"}),"Tambah Transaksi"]}),s.jsx(q,{children:"Tambahkan transaksi penjualan sampah baru"})]}),s.jsxs("form",{onSubmit:te,className:"space-y-4",children:[s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx(N,{htmlFor:"user_name",children:"Nama Nasabah"}),s.jsx(b,{id:"user_name",value:Y.user_name,onChange:e=>ee((a=>({...a,user_name:e.target.value}))),required:!0})]}),s.jsxs("div",{children:[s.jsx(N,{htmlFor:"waste_name",children:"Jenis Sampah"}),s.jsx(b,{id:"waste_name",value:Y.waste_name,onChange:e=>ee((a=>({...a,waste_name:e.target.value}))),required:!0})]})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx(N,{htmlFor:"weight",children:"Berat (Kg)"}),s.jsx(b,{id:"weight",type:"number",step:"0.1",value:Y.weight,onChange:e=>ee((a=>({...a,weight:e.target.value}))),required:!0})]}),s.jsxs("div",{children:[s.jsx(N,{htmlFor:"total_price",children:"Total Harga (Rp)"}),s.jsx(b,{id:"total_price",type:"number",value:Y.total_price,onChange:e=>ee((a=>({...a,total_price:e.target.value}))),required:!0})]})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx(N,{htmlFor:"total_points",children:"Total Poin"}),s.jsx(b,{id:"total_points",type:"number",value:Y.total_points,onChange:e=>ee((a=>({...a,total_points:e.target.value}))),required:!0})]}),s.jsxs("div",{children:[s.jsx(N,{htmlFor:"date",children:"Tanggal Transaksi"}),s.jsx(b,{id:"date",type:"date",value:Y.date,onChange:e=>ee((a=>({...a,date:e.target.value}))),required:!0})]})]}),s.jsxs(B,{children:[s.jsx(_,{type:"button",variant:"outline",onClick:()=>z(!1),children:"Batal"}),s.jsx(_,{type:"submit",className:"btn-primary",children:"Tambah Transaksi"})]})]})]})}),s.jsx(S,{open:G,onOpenChange:M,children:s.jsxs(C,{className:"sm:max-w-md",children:[s.jsxs(F,{children:[s.jsxs(D,{className:"flex items-center gap-2",children:[s.jsx(m,{className:"w-5 h-5 text-blue-600"}),"Edit Transaksi"]}),s.jsx(q,{children:"Perbarui informasi transaksi"})]}),s.jsxs("form",{onSubmit:te,className:"space-y-4",children:[s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx(N,{htmlFor:"edit_user_name",children:"Nama Nasabah"}),s.jsx(b,{id:"edit_user_name",value:Y.user_name,onChange:e=>ee((a=>({...a,user_name:e.target.value}))),required:!0})]}),s.jsxs("div",{children:[s.jsx(N,{htmlFor:"edit_waste_name",children:"Jenis Sampah"}),s.jsx(b,{id:"edit_waste_name",value:Y.waste_name,onChange:e=>ee((a=>({...a,waste_name:e.target.value}))),required:!0})]})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx(N,{htmlFor:"edit_weight",children:"Berat (Kg)"}),s.jsx(b,{id:"edit_weight",type:"number",step:"0.1",value:Y.weight,onChange:e=>ee((a=>({...a,weight:e.target.value}))),required:!0})]}),s.jsxs("div",{children:[s.jsx(N,{htmlFor:"edit_total_price",children:"Total Harga (Rp)"}),s.jsx(b,{id:"edit_total_price",type:"number",value:Y.total_price,onChange:e=>ee((a=>({...a,total_price:e.target.value}))),required:!0})]})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx(N,{htmlFor:"edit_total_points",children:"Total Poin"}),s.jsx(b,{id:"edit_total_points",type:"number",value:Y.total_points,onChange:e=>ee((a=>({...a,total_points:e.target.value}))),required:!0})]}),s.jsxs("div",{children:[s.jsx(N,{htmlFor:"edit_date",children:"Tanggal Transaksi"}),s.jsx(b,{id:"edit_date",type:"date",value:Y.date,onChange:e=>ee((a=>({...a,date:e.target.value}))),required:!0})]})]}),s.jsxs(B,{children:[s.jsx(_,{type:"button",variant:"outline",onClick:()=>M(!1),children:"Batal"}),s.jsx(_,{type:"submit",className:"btn-primary",children:"Simpan Perubahan"})]})]})]})}),s.jsx(P,{isOpen:!!V,onClose:()=>X(null),onConfirm:()=>{V&&($((e=>e.filter((e=>e.id!==V.id)))),A.success("Transaksi berhasil dihapus!",{description:`Transaksi ${V.user_name} telah dihapus dari sistem`}),X(null))},title:"Hapus Transaksi",description:`Apakah Anda yakin ingin menghapus transaksi ${null==V?void 0:V.user_name}? Tindakan ini tidak dapat dibatalkan.`,confirmText:"Hapus",cancelText:"Batal",type:"danger"})]}):null};export{R as default};
